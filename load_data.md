# AiCombinationGraphCache 数据加载性能优化建议

## 问题分析

通过对 `AiCombinationGraphCache` 和相关类的代码分析，发现从本地文件加载 zip 资源数据耗时 10 分钟的主要原因如下：

### 1. 串行加载导致的性能瓶颈

**问题位置**: `AiCombinationGraphCache.loadAiCombinationGraphCache()` 方法

```java
// 当前实现：串行处理每个学段学科
for (String phaseSubject : phaseSubjects) {
    // 串行查询所有UNIT
    for (String cata : unitPoints) {
        // 串行查询每个book和press
        GraphData bookData = queryBook(book);
        GraphData pressBookData = queryPressBook(press);
    }
    // 串行处理所有锚点
    for (String anchorPointId : anchorPointIds) {
        setCache(ANCHOR_POINT, anchorPointId);
    }
    // 串行处理所有考点
    for (String checkPointId : checkPointIds) {
        setCache(CHECK_POINT, checkPointId);
    }
}
```

**性能影响**: 
- 大量的图谱查询操作串行执行
- 每次查询都需要等待前一个查询完成
- 无法充分利用多核CPU资源

### 2. 重复的图谱查询操作

**问题位置**: `queryBook()` 和 `queryPressBook()` 方法

```java
// 每次调用都创建新的SubGraphQuery对象
SubGraphQuery subGraphQuery = new SubGraphQuery();
subGraphQuery.setTraceId(UUID.randomUUID().toString());
// 重复的边标签构建
List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(BOOK).target(UNIT).build());
// ... 多个重复的边标签添加
```

**性能影响**:
- 重复创建对象导致GC压力
- 相同的边标签重复构建
- 缺少查询结果缓存机制

### 3. 堆外内存频繁序列化/反序列化

**问题位置**: `GraphAddFileCache.processZipFile()` 方法

```java
// 频繁的序列化操作
ByteBuffer topicBuffer = offHeapVertexMap.get(topicId);
GraphData.GraphVertex topicInfo = OffHeapObjUtils.readObjectFromOffHeap(topicBuffer);
// 修改后重新序列化
offHeapVertexMap.put(topicId, OffHeapObjUtils.writeObjectToOffHeap(topicInfo));
```

**性能影响**:
- 大量的序列化/反序列化操作消耗CPU
- 堆外内存的频繁分配和释放
- 缺少批量处理机制

### 4. 同步等待机制效率低下

**问题位置**: `AiCombinationGraphCache.initAiCombinationGraphCache()` 方法

```java
// 轮询等待，效率低下
while (!graphAddFileCache.isUpdateCacheFlag()) {
    Thread.sleep(5000);
}
```

**性能影响**:
- 5秒的固定等待时间过长
- 无法及时响应缓存完成事件
- 阻塞主线程执行

## 优化建议

### 1. 并行化数据加载 (优先级: 高)

**实施方案**:
```java
// 使用线程池并行处理
private final ExecutorService executorService = Executors.newFixedThreadPool(
    Math.min(Runtime.getRuntime().availableProcessors(), 8));

private void loadAiCombinationGraphCacheParallel() {
    List<CompletableFuture<Void>> futures = new ArrayList<>();
    
    for (String phaseSubject : phaseSubjects) {
        CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
            loadPhaseSubjectData(phaseSubject);
        }, executorService);
        futures.add(future);
    }
    
    // 等待所有任务完成
    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
}

private void loadPhaseSubjectData(String phaseSubject) {
    // 并行处理锚点和考点
    List<CompletableFuture<Void>> pointFutures = new ArrayList<>();
    
    // 并行处理锚点
    CompletableFuture<Void> anchorFuture = CompletableFuture.runAsync(() -> {
        anchorPointIds.parallelStream().forEach(anchorPointId -> {
            setCache(ANCHOR_POINT, anchorPointId);
        });
    }, executorService);
    
    // 并行处理考点
    CompletableFuture<Void> checkFuture = CompletableFuture.runAsync(() -> {
        checkPointIds.parallelStream().forEach(checkPointId -> {
            setCache(CHECK_POINT, checkPointId);
        });
    }, executorService);
    
    CompletableFuture.allOf(anchorFuture, checkFuture).join();
}
```

**预期效果**: 减少 60-80% 的加载时间

### 2. 实现查询结果缓存 (优先级: 高)

**实施方案**:
```java
// 添加查询结果缓存
private final ConcurrentHashMap<String, SubGraphQuery> queryTemplateCache = new ConcurrentHashMap<>();
private final ConcurrentHashMap<String, GraphData> queryResultCache = new ConcurrentHashMap<>();

private SubGraphQuery getBookQueryTemplate() {
    return queryTemplateCache.computeIfAbsent("BOOK_QUERY", k -> {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setGraphVersion(graphVersion);
        subGraphQuery.setRootVertexLabel(BOOK);
        
        List<SubGraphQuery.EdgeLabel> edgeLabels = Arrays.asList(
            SubGraphQuery.EdgeLabel.builder().source(BOOK).target(UNIT).build(),
            SubGraphQuery.EdgeLabel.builder().source(UNIT).target(COURSE).build(),
            // ... 其他边标签
        );
        subGraphQuery.setEdgeLabels(edgeLabels);
        return subGraphQuery;
    });
}

private GraphData queryBookWithCache(String book) {
    String cacheKey = "BOOK_" + book;
    return queryResultCache.computeIfAbsent(cacheKey, k -> {
        SubGraphQuery query = getBookQueryTemplate().clone();
        query.setTraceId(UUID.randomUUID().toString());
        query.setRootVertexIdList(Arrays.asList(book));
        return localGraphService.querySubGraph(query);
    });
}
```

**预期效果**: 减少 30-50% 的重复查询时间

### 3. 批量处理堆外内存操作 (优先级: 中)

**实施方案**:
```java
// 批量处理缓存更新
private static final int BATCH_SIZE = 1000;

private void batchUpdateCache(Map<String, GraphData> updates) {
    List<Map.Entry<String, GraphData>> entries = new ArrayList<>(updates.entrySet());
    
    // 分批处理
    for (int i = 0; i < entries.size(); i += BATCH_SIZE) {
        int end = Math.min(i + BATCH_SIZE, entries.size());
        List<Map.Entry<String, GraphData>> batch = entries.subList(i, end);
        
        // 批量序列化
        Map<String, ByteBuffer> batchBuffers = batch.parallelStream()
            .collect(Collectors.toConcurrentMap(
                Map.Entry::getKey,
                entry -> {
                    try {
                        return OffHeapObjUtils.writeObjectToOffHeap(entry.getValue());
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            ));
        
        // 批量更新缓存
        batchBuffers.forEach(ANCHOR_POINT_TO_TOPIC_CACHE::put);
    }
}
```

**预期效果**: 减少 20-30% 的序列化时间

### 4. 优化同步等待机制 (优先级: 中)

**实施方案**:
```java
// 使用CountDownLatch替代轮询
private final CountDownLatch cacheInitLatch = new CountDownLatch(1);

// 在GraphAddFileCache中
@PostConstruct
public void initFileGraph() {
    try {
        // ... 初始化逻辑
        updateCacheFlag = true;
        cacheInitLatch.countDown(); // 通知等待线程
    } catch (Exception e) {
        // 错误处理
    }
}

// 在AiCombinationGraphCache中
@PostConstruct
public void initAiCombinationGraphCache() {
    try {
        // 等待缓存初始化完成，最多等待10分钟
        if (!cacheInitLatch.await(10, TimeUnit.MINUTES)) {
            throw new RuntimeException("缓存初始化超时");
        }
        loadAiCombinationGraphCache();
    } catch (Exception e) {
        log.error("AI组卷二级缓存加载失败", e);
    }
}
```

**预期效果**: 减少不必要的等待时间

### 5. 内存使用优化 (优先级: 中)

**实施方案**:
```java
// 优化内存使用
@Value("${ai.combination.cache.initial-capacity:10000}")
private int initialCapacity;

@Value("${ai.combination.cache.load-factor:0.75}")
private float loadFactor;

// 使用更合适的初始容量
protected static final ConcurrentHashMap<String, GraphData> BOOK_CACHE = 
    new ConcurrentHashMap<>(initialCapacity, loadFactor);

// 及时清理不需要的缓存
private void cleanupTemporaryCache() {
    // 清理临时查询结果缓存
    queryResultCache.clear();
    
    // 强制GC（谨慎使用）
    if (Runtime.getRuntime().freeMemory() < Runtime.getRuntime().totalMemory() * 0.1) {
        System.gc();
    }
}
```

**预期效果**: 减少 15-25% 的内存使用

### 6. 监控和诊断优化 (优先级: 低)

**实施方案**:
```java
// 添加详细的性能监控
private void loadAiCombinationGraphCacheWithMonitoring() {
    long start = System.currentTimeMillis();
    
    try (MDCCloseable mdc = MDC.putCloseable("operation", "cache-loading")) {
        log.info("开始加载AI组卷缓存，学段学科数量: {}", phaseSubjects.size());
        
        for (String phaseSubject : phaseSubjects) {
            long phaseStart = System.currentTimeMillis();
            loadPhaseSubjectData(phaseSubject);
            long phaseTime = System.currentTimeMillis() - phaseStart;
            
            log.info("学段学科 {} 加载完成，耗时: {}ms", phaseSubject, phaseTime);
        }
        
        long totalTime = System.currentTimeMillis() - start;
        log.info("AI组卷缓存加载完成，总耗时: {}ms, 缓存大小: book={}, anchor={}, check={}", 
                totalTime, BOOK_CACHE.size(), 
                ANCHOR_POINT_TO_TOPIC_CACHE.size(), 
                CHECK_POINT_TO_TOPIC_CACHE.size());
    }
}
```

**预期效果**: 提供详细的性能分析数据

## 配置优化建议

### 1. JVM参数优化
```bash
# 增加堆内存
-Xms4G -Xmx8G

# 优化GC参数
-XX:+UseG1GC
-XX:MaxGCPauseMillis=200
-XX:G1HeapRegionSize=16m

# 优化堆外内存
-XX:MaxDirectMemorySize=2G
```

### 2. 应用配置优化
```properties
# 增加线程池大小
ai.combination.cache.thread-pool-size=8

# 优化缓存配置
ai.combination.cache.initial-capacity=20000
ai.combination.cache.load-factor=0.75

# 启用批量处理
ai.combination.cache.batch-size=1000
ai.combination.cache.batch-enabled=true
```

## 实施优先级和预期效果

| 优化项 | 优先级 | 实施难度 | 预期性能提升 | 实施时间 |
|--------|--------|----------|--------------|----------|
| 并行化数据加载 | 高 | 中 | 60-80% | 2-3天 |
| 查询结果缓存 | 高 | 低 | 30-50% | 1-2天 |
| 批量处理优化 | 中 | 中 | 20-30% | 2-3天 |
| 同步等待优化 | 中 | 低 | 10-20% | 1天 |
| 内存使用优化 | 中 | 低 | 15-25% | 1天 |
| 监控诊断优化 | 低 | 低 | 0% (便于调试) | 1天 |

**总体预期效果**: 通过实施前3项优化，预计可以将加载时间从10分钟减少到2-3分钟，性能提升70-85%。

## 风险评估和注意事项

1. **并发安全**: 并行化处理时需要确保线程安全
2. **内存使用**: 并行处理可能增加内存使用峰值
3. **错误处理**: 需要完善异常处理机制
4. **向后兼容**: 确保优化不影响现有功能
5. **测试验证**: 需要充分的性能测试和功能测试

## 建议实施步骤

1. **第一阶段**: 实施查询结果缓存和同步等待优化（风险低，收益明显）
2. **第二阶段**: 实施并行化数据加载（需要充分测试）
3. **第三阶段**: 实施批量处理和内存优化
4. **第四阶段**: 添加监控和诊断功能

通过分阶段实施，可以逐步验证优化效果，降低实施风险。
