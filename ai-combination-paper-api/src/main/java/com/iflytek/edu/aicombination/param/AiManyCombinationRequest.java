package com.iflytek.edu.aicombination.param;

import com.iflytek.edu.aicombination.base.SceneInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 *
 * @ClassName AiManyCombinationRequest
 * @description AI组件服务 组多套卷请求结构
 */
@Data
public class AiManyCombinationRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "traceId不能为空")
    private String traceId;

    /**
     *  场景信息
     *  放和引擎功能无关的的参数（用于获取数据的维度，比如用户id，学科学段）
     */
    @NotNull(message = "sceneInfo不能为空")
    private SceneInfo sceneInfo;

    @NotNull(message = "requestBody不能为空")
    private Intention requestBody;

    /**
     * 试卷使用场景 枚举值： HOMEPAGE（首页）、CONVERSATION（对话）；首页推荐传HOMEPAGE
     */
    @NotBlank(message = "试卷使用场景不能为空")
    private String form;

    /**
     * 会话ID  //  推卷记录表过期时间一周
     */
    private String sessionId ;


    /**
     * 首页推卷用
     */
    private List<String> repeatTopics;
}
