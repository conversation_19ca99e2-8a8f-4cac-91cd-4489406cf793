package com.iflytek.edu.aicombination.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class NodeInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 试题ID（取自图谱）
     */
    private String nodeId;

    /**
     * 试题题型编码code：选择、填空。。。
     */
    private String topicType;

    /**
     * 该试题来源 是否错题 true=是
     */
    private Boolean isWrongTopic;

    /**
     * 根据请求中的相关点类型，取自图谱
     */
    private RelationNodeInfo relationNodes;

    /**
     * 分组逻辑：考虑端上展示，需要按照一定规则进行分组
     * group1表示分组1;group2表示分组2;当前最多有2个分组
     * 定制卷:group1=锚点;group2=题型
     * 达标/提优/推荐卷:group1=题型
     * 通用题包:group1=基础巩固/拔高提升;group2=题型
     * 比如,当前为定制卷试题,该题分组为：
     * group1=平行四边形
     * group2=选择题
     */
    private Map<String,String> group;

}
