package com.iflytek.edu.aicombination.param;

import com.iflytek.edu.aicombination.base.SceneInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 *
 *
 * @description AI组件服务请求结构：查询用户书下的薄弱点个数
 *
 */
@Data
public class WeakPointsRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "traceId不能为空")
    private String traceId;

    /**
     *  场景信息
     *  放和引擎功能无关的的参数（用于获取数据的维度，比如用户id，学科学段）
     */
    @NotNull(message = "sceneInfo不能为空")
    private SceneInfo sceneInfo;

}
