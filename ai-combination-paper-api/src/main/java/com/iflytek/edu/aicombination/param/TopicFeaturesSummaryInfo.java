package com.iflytek.edu.aicombination.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TopicFeaturesSummaryInfo implements Serializable {

    private static final long serialVersionUID = -366424399744881312L;

    private Map<String, List<TopicFeature>> topicFeaturesMap;

    private String summary;
}
