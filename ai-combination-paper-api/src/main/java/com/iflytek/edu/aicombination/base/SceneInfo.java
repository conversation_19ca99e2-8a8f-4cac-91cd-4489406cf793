package com.iflytek.edu.aicombination.base;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Description 场景信息类，用于保存场景信息
 */
@Data
@Accessors(chain = true)
public class SceneInfo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 用户ID（用户中台 或 业务自定义）
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 业务方ID（服务层定义）
     * xxj-aizj 学习机大模型AI组卷
     */
    private String appId;

    /**
     * 业务方功能（服务层定义）,默认=AI_PACK_REC_PACK，本场景仅支持传（AI_PACK_REC_PACK）
     */
    private String bizAction;

    /**
     * 业务方代码（服务层定义）
     */
    private String bizCode = "ZSY_XXJ";

    /**
     * 学科代码（学科资源中心定义）
     */
    @NotBlank(message = "学科代码不能为空")
    private String subjectCode;

    /**
     * 学段编码（学科资源中心定义）
     */
    @NotBlank(message = "学段编码不能为空")
    private String phaseCode;

    /**
     * 教材书本（学科资源中心定义）
     */
    private String bookCode;

    /**
     * 教材书本名字（学科资源中心定义）如：数学人教四年级上册（2024年更新）
     */
    private String bookName;

    /**
     * 区域编号（学科资源中心定义）
     */
    private String areaCode;


    /**
     * 区域名字（学科资源中心定义） 如：菏泽市
     */
    private String areaName;

    /**
     * 学习场景（服务层定义）
     */
    private String studyCode;

    /**
     * 图谱版本（数据层定义）
     */
    private String graphVersion;

    /**
     * 教材版本（学科资源中心定义）
     * 暂时未用到，非必填项
     */
    private String pressCode;

    /**
     * 年级
     */
    private String grade;


}
