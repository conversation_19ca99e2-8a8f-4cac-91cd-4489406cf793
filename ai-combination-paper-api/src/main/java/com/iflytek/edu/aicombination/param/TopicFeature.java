package com.iflytek.edu.aicombination.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class TopicFeature implements Serializable {

    private static final long serialVersionUID = 7047185614335387819L;
    /**
     * 试题标签名称
     */
    private String featureName;

    /**
     * 标签所属类型：个人(userFeature)、试题(topicFeature)
     * eg:topicFeature
     */
    private String featureType;

    /**
     * 标签展示顺序
     */
    private int order;
}
