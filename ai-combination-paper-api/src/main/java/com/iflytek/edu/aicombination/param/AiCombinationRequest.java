package com.iflytek.edu.aicombination.param;

import com.iflytek.edu.aicombination.base.SceneInfo;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiCombinationRequest
 * @description AI组件服务请求结构
 * @date 2024/5/29 10:48
 */
@Data
public class AiCombinationRequest implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotBlank(message = "traceId不能为空")
    private String traceId;

    /**
     *  场景信息
     *  放和引擎功能无关的的参数（用于获取数据的维度，比如用户id，学科学段）
     */
    @NotNull(message = "sceneInfo不能为空")
    private SceneInfo sceneInfo;

    @NotNull(message = "requestBody不能为空")
    private Intention requestBody;

}
