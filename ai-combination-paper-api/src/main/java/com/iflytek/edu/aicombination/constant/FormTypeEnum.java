package com.iflytek.edu.aicombination.constant;

/**
 * <AUTHOR>
 *
 * @description 试卷使用场景
 */
public enum FormTypeEnum {
    ORIGINAL("ORIGINAL", "老的组卷接口"),
    HOMEPAGE("HOMEPAGE", "首页"),
    CONVERSATION("CONVERSATION", "对话");

    private String code;
    private String message;
    FormTypeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
