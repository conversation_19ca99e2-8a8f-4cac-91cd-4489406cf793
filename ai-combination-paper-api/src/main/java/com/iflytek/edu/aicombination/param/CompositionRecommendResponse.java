package com.iflytek.edu.aicombination.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


@Data
@Accessors(chain = true)
public class CompositionRecommendResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 试题列表
     */
    private List<NodeInfo> outNodeInfos;

    /**
     * 试题类型 TOPIC
     */
    private String outNodeType = "TOPIC";

    /**
     * 组卷类型 pack=题包 或 paper=组卷
     */
    private String compositionType;

    private String edition;

    /**
     * 大模型试卷总结 入参内容
     */
    private String summary;

    /**
     * 试卷枚举值：达标卷STANDARD_PAPER、拔高卷IMPROVE_PAPER、本地个人薄弱专练LOCAL_PERSON_WEAK_SPECIALIZED_TRAINING、本次常考点专练THIS_FREQUENTLY_POINT_TRAINING、本地易错点专练LOCAL_EASY_WRONG_TRAINING
     * 题包枚举值：题包QUESTION_PACK
     * 首页试卷枚举值：期中卷MIDTERM_PAPER、期末卷 TERMI
     */
    private String paperType;

    /**
     * 试卷标题：
     * 标题逻辑，此处标题与用户输入相关：
     * 情况1、识别用户意图为章/单元范围 标题展示为单元数字+名称：eg1 用户要“第一单元的题目”或“升与毫升的题目”，标题展示为“第一单元 升与毫升”
     * 情况2、识别用户意图为课时/小节范围，标题直接展示名称：eg2 用户要“认识容量和升的题目”，标题展示为“认识容量和升”
     * 情况3、识别用户意图为期中/期末范围，标题展示为年份+年级+学期+期中/期末+数学+定制+达标/提优卷”——举例：2025年四年级（下）期中数学定制达标卷
     * 规则：
     * 1.当anchor不为未知时:
     * 1.1 若others包含常考点/易错点/薄弱点/新课标点，试卷名字为"常考点/易错点/薄弱点/新情景素养专练"
     * 1.2 否则 "定制专练"
     *
     * 2.当range属于单元、期中、期末时。
     * 2.1 若range为单元时
     * 名字为
     * 检索到的图谱中的单元/课时/小节名字+" 单元/空"+达标/提优卷"
     *     若多个单元时，直接使用意图的范围/自己组装  看怎么方便
     *     若检索到的是课时或者小节 不需要单元这两个字
     *
     * 【个人薄弱点专练】+年级+(学期：上/下)+"定制"
     * 【本地高频考点专练】+年级+(学期：上/下)+"定制"
     * 【本地常考点专练】+年级+(学期：上/下)+"定制"
     * 【新情景素养专练】+年级+(学期：上/下)+"定制"
     *
     * 例如
     * 第一单元 升与毫升 单元达标卷
     * 第2课时 升与毫升 达标卷
     * 第1-3单元 单元达标卷
     * 【个人薄弱点专练】四年级(上)定制
     *
     * 2.2 若为期中/期末
     * 名字为
     * 当前年份+年级+(学期：上/下)+范围+"数学定制达标/提优卷"
     * 【个人薄弱点专练】+年级+(学期：上/下)+范围+定制
     * 【本地高频考点专练】+年级+(学期：上/下)+范围+定制
     * 【本地常考点专练】+年级+(学期：上/下)+范围+定制
     * 【新情景素养专练】+年级+(学期：上/下)+范围+定制
     * 例如
     * 2025年四年级(下)期末数学定制达标卷
     * 【个人薄弱点专练】四年级(下)期末定制
     *
     * 3.当range属于升学考时
     * 2025年小升初专练
     * 4.其它情况一律 定制专练  兜底
     */
    private String paperName;

    /**
     * 枚举值：UNIT_EXAM("单元", "UNIT_EXAM"),
     * MID_TERM("期中", "MID_TERM"),
     * FINAL_TERM("期末", "FINAL_TERM"),
     * MONTH_EXAM("月考", "MONTH_EXAM");
     */
    private String rangeType;
}
