package com.iflytek.edu.aicombination.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CompositionResponse implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 组卷内容 题包或试卷 N套
     */
    private List<CompositionRecommendResponse> compositionResult;


    /**
     * 大模型组卷可解释 入参内容
     */
    private String explain;

    /**
     * 组卷结果compositionResult不空: None（默认值）
     * 组卷结果compositionResult为空 返空原因:
     * overKnowledge :超纲
     * lowsimilarity :知识点相似度低
     * general：常规
     */
    private String refuse;

    /**
     * 根据返空原因给出一段描述
     */
    private String refuseDescribe;
}
