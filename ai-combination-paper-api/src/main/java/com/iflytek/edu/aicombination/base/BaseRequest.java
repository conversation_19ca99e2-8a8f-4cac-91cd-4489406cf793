package com.iflytek.edu.aicombination.base;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseRequest
 * @date 2024/5/29 10:40
 */
@Data
@NoArgsConstructor
public class BaseRequest<T extends Serializable> implements Serializable {
    private static final long serialVersionUID = 1L;

    @NotNull
    private String traceId;

    /**
     *  场景信息
     *  放和引擎功能无关的的参数（用于获取数据的维度，比如用户id，学科学段）
     */
    @NotNull
    private SceneInfo sceneInfo;

    @NotNull
    private T requestBody;

}
