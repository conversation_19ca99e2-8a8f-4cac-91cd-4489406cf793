package com.iflytek.edu.aicombination.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 模型意图解析后透传
 */
@Data
@Accessors(chain = true)
public class Intention implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 接口名：compose、similarity、search
     */
    private String plugin;
    /**
     * "功能名：pack、paper"
     */
    private String function;
    /**
     * 锚点名称
     */
    private String anchor;
    /**
     * 章节名称
     */
    private String catalog;
    /**
     * 区域名称
     */
    private String domain;
    /**
     * 版本编码
     */
    private String edition;
    /**
     * 年级编码
     */
    private String grade;
    /**
     * 上下册
     */
    private String book;
    /**
     * 范围：单元、月考、期中、期末、升学考
     */
    private String range;
    /**
     * 需求：题、相似题、试卷、相似卷、真题卷
     */
    private String require;
    /**
     * 题量编码
     */
    private String count;
    /**
     * 难度：难中易
     */
    private String difficulty;
    /**
     * 选择、填空、判断、操作、计算、解答
     */
    private String type;
    /**
     * 易错题、常考题、薄弱点、变难、变简单、增加题量、减少题量
     */
    private String others;

    /**
     * 用户问题 如：给我找一篇第5单元平行四边形与梯形的试卷（组多套试卷接口必传）  首页： ""
     */
    private String query;
}
