package com.iflytek.edu.aicombination.constant;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName ResponseEnum
 * @description 相应枚举类
 * @date 2024/5/29 11:13
 */
public enum ResponseEnum {
    SUCCESS(0, "成功"),
    ERROR(-1, "失败");

    private int code;
    private String message;
    ResponseEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
