package com.iflytek.edu.aicombination.service;

import com.iflytek.edu.aicombination.param.AiCombinationRequest;
import com.iflytek.edu.aicombination.param.AiCombinationResponse;
import com.iflytek.edu.aicombination.param.AiManyCombinationRequest;
import com.iflytek.edu.aicombination.param.ManyCombinationResponse;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryRequest;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryResponse;
import com.iflytek.edu.aicombination.param.WeakPointsRequest;
import com.iflytek.edu.aicombination.param.WeakPointsResponse;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiCombinationService
 * @description Ai组卷接口
 * @date 2024/6/4 10:54
 */
public interface AiCombinationService {

    AiCombinationResponse getAiCombination(AiCombinationRequest aiCombinationRequest);


    ManyCombinationResponse getManyAiCombination(AiManyCombinationRequest aiManyCombinationRequest);

    WeakPointsResponse getWeakPointsInfo(WeakPointsRequest weakPointsRequest);


    TopicFeaturesSummaryResponse getTopicFeaturesAndSummary(TopicFeaturesSummaryRequest topicFeaturesSummaryRequest);

}
