package com.iflytek.edu.aicombination.base;

import com.iflytek.edu.aicombination.constant.ResponseEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName BaseResponse
 * @date 2024/5/29 10:42
 */
@Data
public class BaseResponse<T extends Serializable> implements Serializable {
    private static final long serialVersionUID = 1L;

    private String traceId;

    /**
     * 返回码
     */
    private int code;
    /**
     * 返回信息
     */
    private String msg;
    /**
     * 返回数据
     */
    private T data;

    public void success(T data) {
        this.code = ResponseEnum.SUCCESS.getCode();
        this.msg = ResponseEnum.SUCCESS.getMessage();
        this.data = data;
    }

    public void error(String msg) {
        this.code = ResponseEnum.ERROR.getCode();
        this.msg = msg;
    }
}
