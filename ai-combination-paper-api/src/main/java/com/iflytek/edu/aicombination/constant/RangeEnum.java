package com.iflytek.edu.aicombination.constant;

/**
 * <AUTHOR>
 *
 * @description 范围：期中、期末
 */
public enum RangeEnum {
    MIN("MIN", "期中"),
    END("END", "期末");

    private String code;
    private String message;
    RangeEnum(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
