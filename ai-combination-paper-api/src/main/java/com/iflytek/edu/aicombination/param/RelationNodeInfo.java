package com.iflytek.edu.aicombination.param;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * 推荐点的关联点信息
 * 例：输出的点类型是题，指定相关点类型是锚点，这里返回该题关联的锚点信息
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class RelationNodeInfo implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 点ID
     */
    private String nodeId;

    /**
     * 点名称
     */
    private String nodeName;

    /**
     * 点类型 ANCHOR_POINT
     */
    private String nodeType = "ANCHOR_POINT";

    /**
     * 点掌握度
     */
    private Double nodeMastery = -1.0d;

    /**
     * 锚点考频 ：易错点=FALLIBLE_POINT、薄弱点=WEAK_POINT、常考点=EXAM_POINT、其他=OTHERS；
     * 易错点（型） 0：否 1：是 初中数学+小学数学 private String falliblePoint;
     * 是否常考型（点） 0：否 1：是  private String examPoint;
     * 其中薄弱点（掌握度<0.8）；常考点（important_level_name：高 ）；易错点（falliblepoint：1）。
     */
    private List<String> nodeFreq;

    /**
     * 除了课时之外 点所在最末级目录-图谱自带realLastLevelRelationCatas
     */
    private String realLastLevelRelationCatas;



}
