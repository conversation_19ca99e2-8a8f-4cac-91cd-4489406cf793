package com.iflytek.edu.aicombination;

import com.iflytek.edu.aicombination.ability.AiAbilityService;
import com.iflytek.edu.aicombination.adapter.AiCombinationAdapter;
import com.iflytek.edu.aicombination.param.AiCombinationRequest;
import com.iflytek.edu.aicombination.param.CompositionRecommendResponse;
import com.iflytek.edu.aicombination.service.AiCombinationService;
import com.iflytek.rec.composition.ICompositonRecommend;
import com.iflytek.rec.composition.interfaces.impl.CompositionRecommendImpl;
import com.iflytek.rec.composition.interfaces.param.CompositionRecommendRequest;
import com.iflytek.rec.composition.interfaces.param.Intention;
import com.iflytek.rec.composition.interfaces.param.SceneInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @date 2024/6/3
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = AiCombinationPaperAppBoot.class)
public class ICompositonRecommendTest {
    static ICompositonRecommend iAnchorPredictMethod = new CompositionRecommendImpl();

    @Autowired
    private AiCombinationService aiCombinationService;

    @Autowired
    private AiCombinationAdapter aiCombinationAdapter;

    @Autowired
    private static AiAbilityService aiAbilityService;

    @Test
    public void recomendTest() {
        CompositionRecommendRequest compositionRecommendRequest = new CompositionRecommendRequest();
        compositionRecommendRequest.setIntention(
                new Intention()
                        .setAnchor("")
                        .setAnchor("")
                        .setBook("")
                        .setDomain("")
                        .setCatalog("")
                        .setCount("")
                        .setDifficulty("")
                        .setGrade("")
                        .setPlugin("")
                        .setRequire("")
                        .setRange("").setType(new ArrayList<>())
                        .setOthers(new ArrayList<>())
        ).setTraceId("as123").setSceneInfo(
                new SceneInfo().setAreaCode("").setBizCode("")
                        .setBizAction("AI_PACK_REC_PACK").setBookCode("30_09020213-002")
                        .setPhaseCode("").setPressCode("")
                        .setGraphVersion("20240507_003").setStudyCode("AI_PACK")
                        .setSubjectCode("").setUserId("")
        );
//        CompositionRecommendResponse compositionRecommendResponse = iAnchorPredictMethod.recommend(compositionRecommendRequest);
//        System.out.println(compositionRecommendResponse);
    }

    @Test
    public void test() {
        AiCombinationRequest aiCombinationRequest = new AiCombinationRequest();
        aiCombinationRequest.setTraceId("123");
        com.iflytek.edu.aicombination.base.SceneInfo sceneInfo = new com.iflytek.edu.aicombination.base.SceneInfo()
                .setAreaCode("")
                .setBizCode("")
                .setAppId("xxj-aizj")
                .setBizCode("ZSY_XXJ")
                .setBookCode("01_05020101-003")
                .setPhaseCode("04").setPressCode("")
                .setGraphVersion("20240507_003").setStudyCode("AI_PACK")
                .setSubjectCode("03").setUserId("ygyang5");
        aiCombinationRequest.setSceneInfo(sceneInfo);

        com.iflytek.edu.aicombination.param.Intention intention = new com.iflytek.edu.aicombination.param.Intention().setAnchor("")
                .setAnchor("")
                .setBook("")
                .setDomain("")
                .setCatalog("")
                .setCount("")
                .setDifficulty("")
                .setGrade("")
                .setPlugin("")
                .setRequire("")
                .setRange("").setType("")
                .setOthers("");
        aiCombinationRequest.setRequestBody(intention);
        CompositionRecommendRequest compositionRecommendRequest = aiCombinationAdapter.requestAdapter(aiCombinationRequest);
        //AiCombinationResponse aiCombination = aiCombinationService.getAiCombination(aiCombinationRequest);
        //System.out.println(JSON.toJSONString(aiCombination));
    }

    public static void main(String[] args) {
//        AiCombinationRequest aiCombinationRequest = new AiCombinationRequest();
//        aiCombinationRequest.setTraceId("123");
//        com.iflytek.edu.aicombination.base.SceneInfo sceneInfo = new com.iflytek.edu.aicombination.base.SceneInfo()
//                .setAreaCode("")
//                .setBizCode("")
//                .setAppId("xxj-aizj")
//                .setBizCode("ZSY_XXJ")
//                .setBookCode("01_05020101-003")
//                .setPhaseCode("04").setPressCode("")
//                .setGraphVersion("20240507_003").setStudyCode("AI_PACK")
//                .setSubjectCode("03").setUserId("ygyang5");
//        aiCombinationRequest.setSceneInfo(sceneInfo);
//
//        com.iflytek.edu.aicombination.param.Intention intention = new com.iflytek.edu.aicombination.param.Intention().setAnchor("")
//                .setAnchor("")
//                .setBook("")
//                .setDomain("")
//                .setCatalog("")
//                .setCount("")
//                .setDifficulty("")
//                .setGrade("")
//                .setPlugin("")
//                .setRequire("")
//                .setRange("").setType("asdf")
//                .setOthers("asdf");
//        aiCombinationRequest.setRequestBody(intention);
//        System.out.println(JSON.toJSONString(aiCombinationRequest));
        CompositionRecommendResponse compositionRecommendResponse = new CompositionRecommendResponse();
        aiAbilityService.getSummary(compositionRecommendResponse,"","");
    }
    public static void get (){
        CompositionRecommendResponse compositionRecommendResponse = new CompositionRecommendResponse();
        aiAbilityService.getSummary(compositionRecommendResponse,"","");
    }



}
