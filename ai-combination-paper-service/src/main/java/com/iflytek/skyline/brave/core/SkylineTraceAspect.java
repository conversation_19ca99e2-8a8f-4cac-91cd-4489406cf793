package com.iflytek.skyline.brave.core;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Pair;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.base.Stopwatch;
import com.iflytek.skyline.brave.TraceConstant;
import com.iflytek.skyline.brave.TraceUtils;
import com.iflytek.skyline.brave.annotation.SkylineTrace;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.brave.config.BraveProperties;
import com.iflytek.skyline.brave.core.TraceBaseContext;
import com.iflytek.skyline.brave.data.TraceBase;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.reflect.MethodUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR> @version 1.0
 * @ClassName CustomSkylineTraceAspect
 * @Date: 2025/6/16 10:21
 * @Description:
 */
@Slf4j
@Aspect
public class SkylineTraceAspect {
    private final TraceUtils traceUtils;
    private final Map<String, Pair<Method, Method>> methodCache = new ConcurrentHashMap();
    private final BraveProperties braveProperties;

    public SkylineTraceAspect(TraceUtils traceUtils, BraveProperties braveProperties) {
        this.traceUtils = traceUtils;
        this.braveProperties = braveProperties;
    }

    @Around("@annotation(skylineTraceStart)")
    public Object skylineTraceStarter(ProceedingJoinPoint joinPoint, SkylineTraceStarter skylineTraceStart) throws Throwable {
        return traceApiRequest(joinPoint, skylineTraceStart.typePrefix(), true, skylineTraceStart.isSync4Request(), skylineTraceStart.isSync4Response());
    }

    @Around("@annotation(skylineTrace)")
    public Object traceApiRequest(ProceedingJoinPoint joinPoint, SkylineTrace skylineTrace) throws Throwable {
        return traceApiRequest(joinPoint, skylineTrace.typePrefix(), skylineTrace.isInit(), skylineTrace.isSync4Request(), skylineTrace.isSync4Request());
    }

    private Object traceApiRequest(ProceedingJoinPoint joinPoint, String typePrefix, boolean init, boolean isSync4Request, boolean isSync4Response) throws Throwable {
        TraceBaseContext.clear();
        Object[] args = joinPoint.getArgs();
        Map<String, String> tags = new HashMap<>(3);
        // 根据切面的 class
        Logger logger = LoggerFactory.getLogger(joinPoint.getTarget().getClass());
        if (args != null) {
            logger.trace("Request= {}", args);

            // Dubbo请求场景下 HttpServletRequest 为空
            HttpServletRequest request = RequestContextHolder.getRequestAttributes() != null
                    ? ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest()
                    : null;

            if (init && args.length > 0) {
                // 只取第一个参数
                String traceId = (String) getValue(args[0], "getTraceId");
                Assert.notEmpty(traceId, String.format("The [%s] request not contain traceId.", joinPoint.getSignature()));

                Object sceneObj = getValue(args[0], "getSceneInfo");
                if (sceneObj == null) {
                    log.warn("Not found SceneInfo when traceBaseInitializer.[traceId={}]", traceId);
                }
                JSONObject scene = sceneObj == null ? new JSONObject() : (JSONObject) JSON.toJSON(sceneObj);

                TraceBase traceBase = TraceBaseContext.build(traceId, scene, request);
                TraceBaseContext.setTraceBase(traceBase);
            }

            if (braveProperties.isAopEnabled()) {
                if (request != null) {
                    tags.put("url", request.getRequestURI());
                    tags.put("from", request.getRemoteAddr());
                }
                traceUtils.record(String.format("%s%s", typePrefix, TraceConstant.API_REQUEST),  args[0] , tags, isSync4Request);
            }
        }

        if (braveProperties.isAopEnabled()) {
            Stopwatch stopwatch = Stopwatch.createStarted();
            Object result = null;
            try {
                result = joinPoint.proceed();
                return result;
            } finally {
                stopwatch.stop();

                if (logger.isTraceEnabled()) {
                    logger.trace("Response={}", (result != null) ? JSON.toJSONString(result) : null);
                }
                if (logger.isDebugEnabled()) {
                    Method targetMethod = ((MethodSignature) joinPoint.getSignature()).getMethod();
                    logger.debug("[SkylineTrace]TraceId = {} , {}.cost = {}.", traceUtils.getCurrentTraceId(), targetMethod.getName(), stopwatch);
                }
                tags.put("cost", String.valueOf(stopwatch.elapsed(TimeUnit.MILLISECONDS)));
                traceUtils.record(String.format("%s%s", typePrefix, TraceConstant.API_RESPONSE), result, tags, isSync4Response);
            }
        } else {
            return joinPoint.proceed();
        }

    }

    private Object getValue(Object arg, String methodName) throws InvocationTargetException, IllegalAccessException {
        String key = String.format("%s.%s", arg.getClass(), methodName);
        Pair<Method, Method> pair = methodCache.get(key);
        if (pair == null) {
            Method method = MethodUtils.getMatchingMethod(arg.getClass(), methodName);
            pair = new Pair<>(method, method);
            methodCache.putIfAbsent(key, pair);
        }
        return pair.getKey() != null ? pair.getKey().invoke(arg) : null;
    }



}
