package com.iflytek.edu.aicombination.mapper;

import com.iflytek.edu.aicombination.param.TopicFeature;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Mapper
public interface TopicFeatureMapper {

    TopicFeatureMapper INSTANCE = Mappers.getMapper(TopicFeatureMapper.class);

    TopicFeature toTarget(com.iflytek.rec.composition.interfaces.param.TopicFeature source);

    default Map<String, List<TopicFeature>> convert(Map<String, List<com.iflytek.rec.composition.interfaces.param.TopicFeature>> source) {

        Map<String, List<TopicFeature>> result = new HashMap<>();
        source.forEach((k, v) ->
                result.put(k, v.stream().map(this::toTarget).collect(Collectors.toList())));
        return result;
    }
}
