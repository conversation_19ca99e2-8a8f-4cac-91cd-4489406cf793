package com.iflytek.edu.aicombination.ability.response;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class AuthResponse implements Serializable {
    private static final long serialVersionUID = 4073532434489552972L;
    private String retCode;
    private String accessToken;
    private String expiresIn;

    public String getRetCode() {
        return retCode;
    }

    public void setRetCode(String retCode) {
        this.retCode = retCode;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getExpiresIn() {
        return expiresIn;
    }

    public void setExpiresIn(String expiresIn) {
        this.expiresIn = expiresIn;
    }

    @Override
    public String toString() {
        return "AuthResponse{" +
                "retCode='" + retCode + '\'' +
                ", accessToken='" + accessToken + '\'' +
                ", expiresIn='" + expiresIn + '\'' +
                '}';
    }
}
