package com.iflytek.edu.aicombination.ability.base;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AuthBase implements Serializable {

    private static final long serialVersionUID = -815723008799984773L;
    private String appId;
    private String version;
    private String uid;
    private String df;
    private String deviceId;
    private String timestamp;

}
