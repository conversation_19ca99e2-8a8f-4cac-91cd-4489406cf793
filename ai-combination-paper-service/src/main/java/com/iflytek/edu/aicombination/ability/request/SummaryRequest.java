package com.iflytek.edu.aicombination.ability.request;




import com.iflytek.edu.aicombination.ability.base.AbilityBase;
import com.iflytek.edu.aicombination.ability.base.AbilityPayload;
import com.iflytek.edu.aicombination.ability.base.AbilitySceneInfo;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class SummaryRequest implements Serializable {

    private static final long serialVersionUID = -1769190065872743799L;
    /**
     * 请求基本参数
     */
    private AbilityBase base;

    /**
     * 场景参数
     */
    private AbilitySceneInfo sceneInfo;

    /**
     * 请求具体内容
     */
    private AbilityPayload payload;

}
