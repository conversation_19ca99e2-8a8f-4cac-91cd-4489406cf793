package com.iflytek.edu.aicombination.ability.base;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * AI能力平台 :base参数说明
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AbilityContent implements Serializable {

    private static final long serialVersionUID = 4453709905129356707L;

    /**
     * 对话id，通常为uuid
     */
    private String dialogId;
    /**
     * 扩展字段，json格式
     */
    private String extra;
    /**
     * 消息id，通常为uuid
     */
    private String messageId;
    /**
     *
     */
    private List<AbilitySemantics> semantics;
    /**
     *
     */
    private String bizCode;
    /**
     * 用户id
     */
    private String content;
    /**
     * 内容类型 0业务自定义消息体 1文字 2图片 3音频 4视频 5文件 6含文本代码
     */
    private String contentType;

    private String createTime;

    /**
     *会话id，通常为uuid
     */
    private String chatId;

    private String hideFlag;

    private String responseTime;

    /**
     * 链路id，用于追踪日志
     */
    private String traceId;

}
