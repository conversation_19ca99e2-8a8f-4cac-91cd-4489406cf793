package com.iflytek.edu.aicombination.adapter;

import com.alibaba.fastjson2.JSONObject;
import com.iflytek.edu.aicombination.constant.AICombinationConstant;
import com.iflytek.edu.aicombination.constant.FormTypeEnum;
import com.iflytek.edu.aicombination.mapper.AiCombinationMapper;
import com.iflytek.edu.aicombination.param.AiCombinationRequest;
import com.iflytek.edu.aicombination.param.AiCombinationResponse;
import com.iflytek.edu.aicombination.param.AiManyCombinationRequest;
import com.iflytek.edu.aicombination.param.CompositionRecommendResponse;
import com.iflytek.edu.aicombination.param.CompositionResponse;
import com.iflytek.edu.aicombination.param.ManyCombinationResponse;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryRequest;
import com.iflytek.edu.aicombination.param.WeakPoints;
import com.iflytek.edu.aicombination.param.WeakPointsRequest;
import com.iflytek.edu.aicombination.param.WeakPointsResponse;
import com.iflytek.rec.composition.interfaces.param.CompositionRecommendRequest;
import com.iflytek.rec.composition.interfaces.param.Intention;
import com.iflytek.rec.composition.interfaces.param.SceneInfo;
import com.iflytek.rec.composition.interfaces.param.TopicFeaturesRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiCombinationAdapter
 * @description 适配器
 * @date 2024/6/3 10:02
 */
@Slf4j
@Component
public class AiCombinationAdapter {

    public static final String WEIZHI = "未知";

    /**
     * 图谱版本
     */
    @Value("${ai.combination.graph-version}")
    private String graphVersion;

    public final CompositionRecommendRequest requestAdapter(AiCombinationRequest aiCombinationRequest) {
        log.info("aiCombinationRequest请求信息:{}", JSONObject.toJSONString(aiCombinationRequest));
        CompositionRecommendRequest compositionRecommendRequest = new CompositionRecommendRequest();
        compositionRecommendRequest.setTraceId(aiCombinationRequest.getTraceId());

        compositionRecommendRequest.setSceneInfo(buildParam(aiCombinationRequest.getSceneInfo()));
        Intention intention = AiCombinationMapper.INSTANCE.toIntention(aiCombinationRequest.getRequestBody());
        Intention relIntention = intentionCheck(intention);
        compositionRecommendRequest.setIntention(relIntention);
        return compositionRecommendRequest;
    }



    /**
     *  参数转换为引擎需要的参数
     * @param aiManyCombinationRequest
     * @return
     */
    public final CompositionRecommendRequest requestManyAiCombinationAdapter(AiManyCombinationRequest aiManyCombinationRequest) {
        log.info("aiCombinationRequest请求信息:{}", JSONObject.toJSONString(aiManyCombinationRequest));
        CompositionRecommendRequest compositionRecommendRequest = new CompositionRecommendRequest();
        compositionRecommendRequest.setTraceId(aiManyCombinationRequest.getTraceId());

        compositionRecommendRequest.setSceneInfo(buildParam(aiManyCombinationRequest.getSceneInfo()));
        Intention intention = AiCombinationMapper.INSTANCE.toIntention(aiManyCombinationRequest.getRequestBody());
        Intention relIntention = intentionCheck(intention);
        compositionRecommendRequest.setIntention(relIntention);
        compositionRecommendRequest.setForm(aiManyCombinationRequest.getForm());
        //首页是学习机传过来的
        if(FormTypeEnum.HOMEPAGE.getCode().equals(aiManyCombinationRequest.getForm()) && !CollectionUtils.isEmpty(aiManyCombinationRequest.getRepeatTopics())){
            compositionRecommendRequest.setRepeatTopics(aiManyCombinationRequest.getRepeatTopics());
        }
        return compositionRecommendRequest;
    }

    /**
     * 参数转换为引擎需要的参数
     *
     * @param weakPointsRequest
     * @return
     */
    public final com.iflytek.rec.composition.interfaces.param.WeakPointsRequest weakPointsRequestAdapter(WeakPointsRequest weakPointsRequest) {
        log.info("weakPointsRequest请求信息:{}", JSONObject.toJSONString(weakPointsRequest));
        com.iflytek.rec.composition.interfaces.param.WeakPointsRequest pointsRequest = new com.iflytek.rec.composition.interfaces.param.WeakPointsRequest();
        pointsRequest.setTraceId(weakPointsRequest.getTraceId());

        pointsRequest.setSceneInfo(buildParam(weakPointsRequest.getSceneInfo()));
        return pointsRequest;
    }

    /**
     * 参数转换为引擎需要的参数
     *
     * @param topicFeaturesSummaryRequest
     * @return
     */
    public final TopicFeaturesRequest topicFeaturesSummaryRequestAdapter(TopicFeaturesSummaryRequest topicFeaturesSummaryRequest) {
        log.info("topicFeaturesSummaryRequest请求信息:{}", JSONObject.toJSONString(topicFeaturesSummaryRequest));
        TopicFeaturesRequest topicFeaturesRequest = new TopicFeaturesRequest();
        topicFeaturesRequest.setTraceId(topicFeaturesSummaryRequest.getTraceId());

        topicFeaturesRequest.setSceneInfo(buildParam(topicFeaturesSummaryRequest.getSceneInfo()));
        List<com.iflytek.rec.composition.interfaces.param.CompositionRecommendResponse> compositionResult = AiCombinationMapper.INSTANCE.toListCompositionResponse(topicFeaturesSummaryRequest.getCompositionResult());
        topicFeaturesRequest.setCompositionResult(compositionResult);
        return topicFeaturesRequest;
    }

    /**
     * 处理参数
     * @param info
     * @return
     */
    private SceneInfo buildParam(com.iflytek.edu.aicombination.base.SceneInfo info) {
        SceneInfo sceneInfo = AiCombinationMapper.INSTANCE.toSceneInfo(info);
        sceneInfo.setGraphVersion(graphVersion);
        sceneInfo.setStudyCode(AICombinationConstant.AI_COMBINATION_STUDY_CODE);
        sceneInfo.setBizAction(AICombinationConstant.AI_COMBINATION_BIZ_ACTION);
        // bookCode 处理：加版本前缀
        if (StringUtils.isNotEmpty(sceneInfo.getPressCode()) && StringUtils.isNotEmpty(sceneInfo.getBookCode())) {
            String bookCode = sceneInfo.getPressCode() + "_" + sceneInfo.getBookCode();
            sceneInfo.setBookCode(bookCode);
        } else {
            sceneInfo.setBookCode("");
        }
        return sceneInfo;
    }

    public final AiCombinationResponse responseAdapter(String traceId, com.iflytek.rec.composition.interfaces.param.CompositionRecommendResponse compositionRecommendResponse) {
        log.info("traceId:{},ManyCombinationResponse引擎响应信息:{}",traceId, JSONObject.toJSONString(compositionRecommendResponse));
        CompositionRecommendResponse response = AiCombinationMapper.INSTANCE.toAiCombinationResponse(compositionRecommendResponse);
        AiCombinationResponse aiCombinationResponse = new AiCombinationResponse();
        aiCombinationResponse.success(response);
        aiCombinationResponse.setTraceId(traceId);
        return aiCombinationResponse;
    }

    public final ManyCombinationResponse compositionResponseAdapter(String traceId, com.iflytek.rec.composition.interfaces.param.CompositionRes compositionRes) {
        log.info("traceId:{},ManyCombinationResponse引擎响应信息:{}",traceId, JSONObject.toJSONString(compositionRes));
        CompositionResponse response = AiCombinationMapper.INSTANCE.toCompositionResponse(compositionRes);
        ManyCombinationResponse manyCombinationResponse = new ManyCombinationResponse();
        manyCombinationResponse.success(response);
        manyCombinationResponse.setTraceId(traceId);
        return manyCombinationResponse;
    }

    public final WeakPointsResponse weakPointsResponseAdapter(String traceId, com.iflytek.rec.composition.interfaces.param.WeakPointsResponse weakPointsResponse) {
        log.info("traceId:{},ManyCombinationResponse引擎响应信息:{}",traceId, JSONObject.toJSONString(weakPointsResponse));
        WeakPoints response = AiCombinationMapper.INSTANCE.toWeakPointsResponse(weakPointsResponse);
        WeakPointsResponse pointsResponse = new WeakPointsResponse();
        pointsResponse.success(response);
        pointsResponse.setTraceId(traceId);
        return pointsResponse;
    }

    private Intention intentionCheck(Intention intention) {
        if (StringUtils.isEmpty(intention.getAnchor())) {
            intention.setAnchor(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getPlugin())) {
            intention.setPlugin(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getFunction())) {
            intention.setFunction(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getCatalog())) {
            intention.setCatalog(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getDomain())) {
            intention.setDomain(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getEdition())) {
            intention.setEdition(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getGrade())) {
            intention.setGrade(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getBook())) {
            intention.setBook(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getRange())) {
            intention.setRange(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getRequire())) {
            intention.setRequire(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getCount())) {
            intention.setCount(WEIZHI);
        }
        if (StringUtils.isEmpty(intention.getDifficulty())) {
            intention.setDifficulty(WEIZHI);
        }
        if (CollectionUtils.isEmpty(intention.getType())) {
            intention.setType(Collections.singletonList(WEIZHI));
        }
        if (CollectionUtils.isEmpty(intention.getOthers())) {
            intention.setOthers(Collections.singletonList(WEIZHI));
        }
        return intention;
    }
}
