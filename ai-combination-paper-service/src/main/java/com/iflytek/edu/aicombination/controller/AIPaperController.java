package com.iflytek.edu.aicombination.controller;

import com.iflytek.edu.aicombination.param.AiCombinationRequest;
import com.iflytek.edu.aicombination.param.AiCombinationResponse;
import com.iflytek.edu.aicombination.param.AiManyCombinationRequest;
import com.iflytek.edu.aicombination.param.ManyCombinationResponse;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryRequest;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryResponse;
import com.iflytek.edu.aicombination.param.WeakPointsRequest;
import com.iflytek.edu.aicombination.param.WeakPointsResponse;
import com.iflytek.edu.aicombination.service.AiCombinationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AIPaperController
 * @description TODO
 * @date 2024/6/25 15:28
 */
@Slf4j
@RestController
@RequestMapping("/aipaper")
public class AIPaperController {

    @Resource
    private AiCombinationService aiCombinationService;

    @PostMapping("/combination")
    public AiCombinationResponse getCombination(@RequestBody AiCombinationRequest aiCombinationRequest) {
        return aiCombinationService.getAiCombination(aiCombinationRequest);
    }

    /**
     * 首页推荐卷（期中和期末两套试卷）:改为学习机调2次，传推题记录，降低重复题/组多套试卷接口
     * @param aiManyCombinationRequest
     * @return
     */
    @PostMapping("/getManyAiCombination")
    public ManyCombinationResponse getManyAiCombination(@RequestBody AiManyCombinationRequest aiManyCombinationRequest) {
        return aiCombinationService.getManyAiCombination(aiManyCombinationRequest);
    }

    /**
     * 查询用户书下的薄弱点个数：com.iflytek.skylab.core.dataapi.service.MasterService#queryMasterData
     *  画像得分 masterScore  [0，0.8）为薄弱点
     * @param weakPointsRequest
     * @return
     */
    @PostMapping("/getWeakPointsInfo")
    public WeakPointsResponse getWeakPointsInfo(@RequestBody WeakPointsRequest weakPointsRequest) {
        return aiCombinationService.getWeakPointsInfo(weakPointsRequest);
    }

    /**
     * 试题标签和试卷总结
     * @param topicFeaturesSummaryRequest
     * @return
     */
    @PostMapping("/getTopicFeaturesAndSummary")
    public TopicFeaturesSummaryResponse getTopicFeaturesAndSummary(@RequestBody TopicFeaturesSummaryRequest topicFeaturesSummaryRequest) {
        return aiCombinationService.getTopicFeaturesAndSummary(topicFeaturesSummaryRequest);
    }

}
