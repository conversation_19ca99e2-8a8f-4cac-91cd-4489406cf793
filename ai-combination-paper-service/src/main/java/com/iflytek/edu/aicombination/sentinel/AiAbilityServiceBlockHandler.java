package com.iflytek.edu.aicombination.sentinel;

import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.iflytek.edu.aicombination.param.CompositionRecommendResponse;
import lombok.extern.slf4j.Slf4j;


/**
 * AI能力服务熔断降级处理器
 * <AUTHOR>
 */
@Slf4j
public class AiAbilityServiceBlockHandler {


    /**
     * 熔断降级处理方法
     * 注意：必须静态方法，参数列表需要与原方法匹配，最后加一个BlockException参数
     */
    public static String handleSummaryBlock(
            CompositionRecommendResponse compositionRecommendResponse,
            String traceId,
            String userId,
            BlockException ex) {

        log.warn("[Sentinel 熔断降级] AI能力平台接口触发熔断，traceId: {},userId: {}, 原因: {}",
                traceId,userId, ex.getClass().getSimpleName());

        // 返回降级结果（空字符串或缓存数据）
        return getDefaultSummary();
    }


    /**
     * 异常降级处理方法
     */
    public static String handleSummaryFallback(CompositionRecommendResponse compositionRecommendResponse,
                                        String traceId, String userId, Throwable t) {
        log.error("AI能力平台接口调用异常, traceId: {}, userId: {},异常信息: {}", traceId,userId, t.getMessage(), t);
        // 返回空字符串作为降级结果
        return getDefaultSummary();
    }

    /**
     * 已和业务沟通，异常时返回空字符串
     * @return
     */
    private static String getDefaultSummary() {
        return "";
    }
}