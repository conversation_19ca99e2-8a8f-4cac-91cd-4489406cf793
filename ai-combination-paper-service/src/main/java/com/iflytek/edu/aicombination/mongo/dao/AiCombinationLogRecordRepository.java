package com.iflytek.edu.aicombination.mongo.dao;

import com.iflytek.edu.aicombination.mongo.Entity.AiCombinationLogRecordEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class AiCombinationLogRecordRepository {

    private final String COLLECTION_NAME = "xxj_ai_combination_log_record";

    @Autowired
    private MongoTemplate mongoTemplate;

    /**
     * 新增组卷推卷记录
     */
    public void insert(AiCombinationLogRecordEntity entity) {
        entity.setCreateTime(Instant.now());
        mongoTemplate.insert(entity, COLLECTION_NAME);
    }

    /**
     * 根据会话ID查询推卷题集合
     * @param sessionId
     * @return
     */
    public List<String> findTopicsBySessionId(String sessionId) {
        // 创建一个新的查询条件
        Criteria criteria = new Criteria();
        criteria.and("sessionId").is(sessionId);
        Query query = new Query();
        query.addCriteria(criteria);
        // 执行聚合查询
        List<AiCombinationLogRecordEntity> results = mongoTemplate.find(query, AiCombinationLogRecordEntity.class, COLLECTION_NAME);
        List<String> topicIds = new ArrayList<>();
        for (AiCombinationLogRecordEntity entity : results) {
            List<String> topicId = entity.getTopicId();
            if (!CollectionUtils.isEmpty(topicId)) {
                topicIds.addAll(topicId);
            }
        }

        return  topicIds;
    }
}
