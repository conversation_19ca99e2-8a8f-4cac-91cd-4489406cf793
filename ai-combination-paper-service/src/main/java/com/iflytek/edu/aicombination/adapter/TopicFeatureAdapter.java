package com.iflytek.edu.aicombination.adapter;


import com.iflytek.edu.aicombination.mapper.TopicFeatureMapper;

import com.iflytek.edu.aicombination.param.TopicFeature;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @ClassName TopicFeatureAdapter
 * @description 适配器
 */
@Slf4j
@Component
public class TopicFeatureAdapter {


    public final Map<String, List<TopicFeature>> Adapter(Map<String, List<com.iflytek.rec.composition.interfaces.param.TopicFeature>> topicFeaturesMap) {

        return TopicFeatureMapper.INSTANCE.convert(topicFeaturesMap);

    }

}
