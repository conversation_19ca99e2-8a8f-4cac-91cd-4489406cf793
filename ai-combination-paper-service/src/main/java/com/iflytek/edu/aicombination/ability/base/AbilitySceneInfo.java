package com.iflytek.edu.aicombination.ability.base;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * AI能力平台 :场景参数
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AbilitySceneInfo implements Serializable {

    private static final long serialVersionUID = 3960854197870000463L;
    /**
     * 助手编码，由平台提供
     */
    private String sceneCode;
    /**
     * 技能编码，由平台提供，指定技能必传；需调用技能规划时不传
     */
    private String intentionCode;
    /**
     * 学科编码
     */
    private String subject;
    /**
     * 学段编码
     */
    private String phase;
    /**
     * 设备id
     */
    private String grade;



}
