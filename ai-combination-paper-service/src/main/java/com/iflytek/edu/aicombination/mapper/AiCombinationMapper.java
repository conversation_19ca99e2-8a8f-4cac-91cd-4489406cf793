package com.iflytek.edu.aicombination.mapper;

import cn.hutool.core.collection.ListUtil;
import com.alibaba.fastjson2.JSONArray;
import com.iflytek.edu.aicombination.base.SceneInfo;
import com.iflytek.edu.aicombination.param.CompositionRecommendResponse;
import com.iflytek.edu.aicombination.param.CompositionResponse;
import com.iflytek.edu.aicombination.param.Intention;
import com.iflytek.edu.aicombination.param.WeakPoints;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.iflytek.edu.aicombination.adapter.AiCombinationAdapter.WEIZHI;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiCombinationMapper
 * @description TODO
 * @date 2024/6/4 9:52
 */
@Mapper
public interface AiCombinationMapper {
    AiCombinationMapper INSTANCE = Mappers.getMapper(AiCombinationMapper.class);

    @Mapping(source = "userId", target = "userId")
    @Mapping(source = "bizCode", target = "bizCode")
    @Mapping(source = "subjectCode", target = "subjectCode")
    @Mapping(source = "phaseCode", target = "phaseCode")
    @Mapping(source = "areaCode", target = "areaCode")
    @Mapping(source = "areaName", target = "areaName")
    @Mapping(source = "bookCode", target = "bookCode")
    @Mapping(source = "bookName", target = "bookName")
    @Mapping(source = "studyCode", target = "studyCode")
    @Mapping(source = "graphVersion", target = "graphVersion")
    @Mapping(source = "pressCode", target = "pressCode")
    @Mapping(source = "grade", target = "grade")
    com.iflytek.rec.composition.interfaces.param.SceneInfo toSceneInfo(SceneInfo sceneInfo);


    @Mapping(source = "plugin", target = "plugin")
    @Mapping(source = "function", target = "function")
    @Mapping(source = "anchor", target = "anchor")
    @Mapping(source = "catalog", target = "catalog")
    @Mapping(source = "domain", target = "domain")
    @Mapping(source = "edition", target = "edition")
    @Mapping(source = "grade", target = "grade")
    @Mapping(source = "book", target = "book")
    @Mapping(source = "range", target = "range")
    @Mapping(source = "require", target = "require")
    @Mapping(source = "count", target = "count")
    @Mapping(source = "difficulty", target = "difficulty")
    @Mapping(target = "type", expression = "java(stringToList(intention.getType()))")
    @Mapping(target = "others", expression = "java(stringToList(intention.getOthers()))")
    @Mapping(source = "query", target = "query")
    com.iflytek.rec.composition.interfaces.param.Intention toIntention(Intention intention);

    CompositionRecommendResponse toAiCombinationResponse(com.iflytek.rec.composition.interfaces.param.CompositionRecommendResponse compositionRecommendResponse);

    CompositionResponse toCompositionResponse(com.iflytek.rec.composition.interfaces.param.CompositionRes compositionRes);

    List<com.iflytek.rec.composition.interfaces.param.CompositionRecommendResponse> toListCompositionResponse(List<CompositionRecommendResponse> compositionResult);

    WeakPoints toWeakPointsResponse(com.iflytek.rec.composition.interfaces.param.WeakPointsResponse weakPointsResponse);

    // 自定义方法处理String到List<String>的映射
    default List<String> stringToList(String source) {
        List<String> list = new ArrayList<>();
        // 假设这里我们只是将字符串封装成一个列表
        // 如果字符串为空或null，则返回空列表
        if (source == null || source.isEmpty()) {
            return Collections.singletonList(WEIZHI);
        }
        // 将单个字符串封装成列表
        try {
            list = JSONArray.parse(source).toList(String.class);
        } catch (Exception e) {
            // 非"['未知']" 格式
            list = ListUtil.toList(source.split(","));
        }
        return list;
    }

}
