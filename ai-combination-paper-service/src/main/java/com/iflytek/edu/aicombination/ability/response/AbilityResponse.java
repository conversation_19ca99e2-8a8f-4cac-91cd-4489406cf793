package com.iflytek.edu.aicombination.ability.response;

import com.iflytek.edu.aicombination.ability.base.AbilityContent;
import lombok.Data;

import java.io.Serializable;

/**
 * AI 能力平台返回结果
 * <AUTHOR>
 */

@Data
public class AbilityResponse  implements Serializable {

    private static final long serialVersionUID = 7929671646584975650L;

    private String code;

    private String message;
    private String success;
    /**
     * 返回数据
     */
    private AbilityContent data;

}
