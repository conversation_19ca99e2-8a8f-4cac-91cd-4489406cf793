package com.iflytek.edu.aicombination.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.iflytek.edu.aicombination.ability.AiAbilityService;
import com.iflytek.edu.aicombination.adapter.AiCombinationAdapter;
import com.iflytek.edu.aicombination.adapter.TopicFeatureAdapter;
import com.iflytek.edu.aicombination.annotation.ServiceRequestMetrics;
import com.iflytek.edu.aicombination.constant.FormTypeEnum;
import com.iflytek.edu.aicombination.mongo.Entity.AiCombinationLogRecordEntity;
import com.iflytek.edu.aicombination.mongo.dao.AiCombinationLogRecordRepository;
import com.iflytek.edu.aicombination.param.AiCombinationRequest;
import com.iflytek.edu.aicombination.param.AiCombinationResponse;
import com.iflytek.edu.aicombination.param.AiManyCombinationRequest;
import com.iflytek.edu.aicombination.param.ManyCombinationResponse;
import com.iflytek.edu.aicombination.param.NodeInfo;
import com.iflytek.edu.aicombination.param.TopicFeature;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummary;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryInfo;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryRequest;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryResponse;
import com.iflytek.edu.aicombination.param.WeakPointsRequest;
import com.iflytek.edu.aicombination.param.WeakPointsResponse;
import com.iflytek.edu.aicombination.service.AiCombinationService;
import com.iflytek.edu.aicombination.utils.ValidateUtil;
import com.iflytek.rec.composition.ICompositonRecommend;
import com.iflytek.rec.composition.interfaces.impl.CompositionRecommendImpl;
import com.iflytek.rec.composition.interfaces.param.CompositionRecommendRequest;
import com.iflytek.rec.composition.interfaces.param.CompositionRes;
import com.iflytek.rec.composition.interfaces.param.TopicFeaturesRequest;
import com.iflytek.skyline.brave.annotation.SkylineTraceStarter;
import com.iflytek.skyline.common.exception.ParamInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiCombinationServiceImpl
 * @description TODO
 * @date 2024/6/4 13:50
 */
@Slf4j
@Service("aiCombinationService")
public class AiCombinationServiceImpl implements AiCombinationService {

    @Autowired
    private AiCombinationAdapter aiCombinationAdapter;

    @Autowired
    @Lazy
    private AiCombinationLogRecordRepository aiCombinationLogRecordRepository;

    @Autowired
    private TopicFeatureAdapter topicFeatureAdapter;

    @Autowired
    private AiAbilityService aiAbilityService;
    
    @Autowired
    private EngineService engineService;



    @Override
    @ServiceRequestMetrics(desc = "组单套卷")
    @SkylineTraceStarter(typePrefix = "getAiCombination#", isSync4Request = false)
    public AiCombinationResponse getAiCombination(AiCombinationRequest aiCombinationRequest) {
        // 参数验证 大模型理解参数校验
        CompositionRes result;
        AiCombinationResponse aiCombinationResponse = new AiCombinationResponse();
        try {
            ValidateUtil.validatorScence(aiCombinationRequest);
            MDC.put("traceId", aiCombinationRequest.getTraceId());
            CompositionRecommendRequest compositionRecommendRequest = aiCombinationAdapter.requestAdapter(aiCombinationRequest);
            compositionRecommendRequest.setForm(FormTypeEnum.ORIGINAL.getCode());
            long l = System.currentTimeMillis();
            result = engineService.recommend( compositionRecommendRequest);
            log.info("traceId:{},aiCombinationService 引擎调用 cost time:{}", aiCombinationRequest.getTraceId(), System.currentTimeMillis() - l);
            aiCombinationResponse = aiCombinationAdapter.responseAdapter(aiCombinationRequest.getTraceId(),
                    result != null && CollectionUtil.isNotEmpty(result.getCompositionResult()) ? result.getCompositionResult().get(0) : null);
        } catch (ParamInvalidException e) {
            log.error("traceId:{},aiCombinationService 参数异常:{}", aiCombinationRequest.getTraceId(), e.getMessage());
            aiCombinationResponse.error(e.getMessage());
            return aiCombinationResponse;
        } catch (Exception e) {
            log.error("traceId:{},aiCombinationService 引擎调用异常： {}", aiCombinationRequest.getTraceId(), e.getMessage());
            aiCombinationResponse.error("AI组卷引擎异常！");
            return aiCombinationResponse;
        }
        return aiCombinationResponse;
    }


    @Override
    @ServiceRequestMetrics(desc = "组多套卷")
    @SkylineTraceStarter(typePrefix = "getManyAiCombination#", isSync4Request = false)
    public ManyCombinationResponse getManyAiCombination(AiManyCombinationRequest aiManyCombinationRequest) {
        ManyCombinationResponse manyCombinationResponse = new ManyCombinationResponse();
        try {
            ValidateUtil.validatorScenceManyAiCombination(aiManyCombinationRequest);
            MDC.put("traceId", aiManyCombinationRequest.getTraceId());
            manyCombinationResponse.setTraceId(aiManyCombinationRequest.getTraceId());
            CompositionRecommendRequest compositionRecommendRequest = aiCombinationAdapter.requestManyAiCombinationAdapter(aiManyCombinationRequest);
            //只有组多套卷时对话需要存推卷记录 根据sessionId查询组卷推荐记录
            if (FormTypeEnum.CONVERSATION.getCode().equals(aiManyCombinationRequest.getForm()) && StringUtils.isNotEmpty(aiManyCombinationRequest.getSessionId())) {
                setTopicsBySessionId(compositionRecommendRequest, aiManyCombinationRequest);
            }
            log.info("traceId:{},调用引擎compositionRecommendRequest请求信息:{}", aiManyCombinationRequest.getTraceId(),JSONObject.toJSONString(compositionRecommendRequest));
            long l = System.currentTimeMillis();

            CompositionRes result = engineService.manyAiCombination(compositionRecommendRequest);
            log.info("traceId:{},aiCombinationService.getManyAiCombination 引擎调用 cost time:{}", aiManyCombinationRequest.getTraceId(), System.currentTimeMillis() - l);
            manyCombinationResponse = aiCombinationAdapter.compositionResponseAdapter(aiManyCombinationRequest.getTraceId(), result);

            List<com.iflytek.edu.aicombination.param.CompositionRecommendResponse> compositionResult = manyCombinationResponse.getData().getCompositionResult();
            //只有组多套卷时对话需要存推卷记录
            if (FormTypeEnum.CONVERSATION.getCode().equals(aiManyCombinationRequest.getForm())) {
                //把推卷记录放mongo表
                createLogRecordEntity(aiManyCombinationRequest, compositionResult);
            }
        } catch (ParamInvalidException e) {
            log.error("traceId:{},aiCombinationService.getManyAiCombination 参数异常:{}", aiManyCombinationRequest.getTraceId(), e.getMessage());
            manyCombinationResponse.error(e.getMessage());
            return manyCombinationResponse;
        } catch (Exception e) {
            log.error("traceId:{},aiCombinationService.getManyAiCombination 引擎调用异常： {}", aiManyCombinationRequest.getTraceId(), e.getMessage());
            manyCombinationResponse.error("AI组卷引擎异常！");
            return manyCombinationResponse;
        }
        return manyCombinationResponse;
    }


    /**
     * 只有组多套卷时对话需要存推卷记录 根据sessionId查询组卷推荐记录
     *
     * @param compositionRecommendRequest
     * @param aiManyCombinationRequest
     */
    private void setTopicsBySessionId(CompositionRecommendRequest compositionRecommendRequest, AiManyCombinationRequest aiManyCombinationRequest) {
        log.info("traceId:{},查询组卷推荐记录getManyAiCombination.findTopicsBySessionId  请求信息:{}", aiManyCombinationRequest.getTraceId(), aiManyCombinationRequest.getSessionId());
        List<String> repeatTopics = aiCombinationLogRecordRepository.findTopicsBySessionId(aiManyCombinationRequest.getSessionId());
        log.info("traceId:{},查询组卷推荐记录getManyAiCombination.findTopicsBySessionId  响应信息repeatTopics大小:{}", aiManyCombinationRequest.getTraceId(), repeatTopics.size());

        if (CollectionUtil.isNotEmpty(repeatTopics)) {
            //去重
            List<String> distinctRepeatTopics = repeatTopics.stream().distinct().collect(Collectors.toList());
            compositionRecommendRequest.setRepeatTopics(distinctRepeatTopics);
        }
        log.info("traceId:{},aiCombinationRequest组多套卷请求信息:{}", aiManyCombinationRequest.getTraceId(), JSONObject.toJSONString(aiManyCombinationRequest));
    }


    @Override
    @ServiceRequestMetrics(desc = "查询用户书下的薄弱点个数")
    @SkylineTraceStarter(typePrefix = "getWeakPointsInfo#", isSync4Request = false)
    public WeakPointsResponse getWeakPointsInfo(WeakPointsRequest weakPointsRequest) {
        WeakPointsResponse weakPointsResponse = new WeakPointsResponse();
        try {
            ValidateUtil.validatorWeakPoints(weakPointsRequest);
            MDC.put("traceId", weakPointsRequest.getTraceId());
            weakPointsResponse.setTraceId(weakPointsRequest.getTraceId());
            com.iflytek.rec.composition.interfaces.param.WeakPointsRequest weakPointsRequestAdapter = aiCombinationAdapter.weakPointsRequestAdapter(weakPointsRequest);

            long l = System.currentTimeMillis();
            com.iflytek.rec.composition.interfaces.param.WeakPointsResponse result = engineService.weakPointsInfo(weakPointsRequestAdapter);
            log.info("traceId:{},aiCombinationService.getWeakPointsInfo 引擎调用 cost time:{}", weakPointsRequest.getTraceId(), System.currentTimeMillis() - l);
            weakPointsResponse = aiCombinationAdapter.weakPointsResponseAdapter(weakPointsRequest.getTraceId(), result);
        } catch (ParamInvalidException e) {
            log.error("traceId:{},aiCombinationService.getWeakPointsInfo 参数异常:{}", weakPointsRequest.getTraceId(), e.getMessage());
            weakPointsResponse.error(e.getMessage());
            return weakPointsResponse;
        } catch (Exception e) {
            log.error("traceId:{},aiCombinationService.getWeakPointsInfo 引擎调用异常： {}", weakPointsRequest.getTraceId(), e.getMessage());
            weakPointsResponse.error("AI组卷:查询用户书下的薄弱点个数引擎异常！");
            return weakPointsResponse;
        }
        return weakPointsResponse;
    }

    /**
     * 业务方现在是单套试卷参数，引擎支持多套，但AI能力平台只支持单套试卷的总结，现在只处理单套的试卷
     *
     * @param topicFeaturesSummaryRequest
     * @return
     */
    @Override
    @ServiceRequestMetrics(desc = "试题标签和试卷总结")
    @SkylineTraceStarter(typePrefix = "getTopicFeaturesAndSummary#", isSync4Request = false)
    public TopicFeaturesSummaryResponse getTopicFeaturesAndSummary(TopicFeaturesSummaryRequest topicFeaturesSummaryRequest) {
        TopicFeaturesSummaryResponse topicFeaturesSummaryResponse = new TopicFeaturesSummaryResponse();
        try {
            ValidateUtil.validatorTopicFeaturesSummary(topicFeaturesSummaryRequest);
            MDC.put("traceId", topicFeaturesSummaryRequest.getTraceId());
            topicFeaturesSummaryResponse.setTraceId(topicFeaturesSummaryRequest.getTraceId());
            TopicFeaturesRequest topicFeaturesRequest = aiCombinationAdapter.topicFeaturesSummaryRequestAdapter(topicFeaturesSummaryRequest);

            long l = System.currentTimeMillis();
            com.iflytek.rec.composition.interfaces.param.TopicFeaturesResponse result = engineService.topicFeatures(topicFeaturesRequest);
            log.info("traceId:{},aiCombinationService.getTopicFeaturesAndSummary 引擎调用 cost time:{}", topicFeaturesSummaryRequest.getTraceId(), System.currentTimeMillis() - l);

            Map<String, List<TopicFeature>> topicFeaturesMap = topicFeatureAdapter.Adapter(result.getTopicFeaturesMap());
            //调AI能力平台查询试卷总结 只支持单套试卷
            long ln = System.currentTimeMillis();
            TopicFeaturesSummary topicFeaturesSummary = generateTopicFeaturesSummary(topicFeaturesSummaryRequest, topicFeaturesRequest,
                    topicFeaturesMap);
            log.info("traceId:{},aiCombinationService.generateTopicFeaturesSummary 能力平台调用查试卷总结 cost time:{}", topicFeaturesSummaryRequest.getTraceId(), System.currentTimeMillis() - ln);
            topicFeaturesSummaryResponse.success(topicFeaturesSummary);
        } catch (ParamInvalidException e) {
            log.error("traceId:{},aiCombinationService.getTopicFeaturesAndSummary 参数异常:{}", topicFeaturesSummaryRequest.getTraceId(), e.getMessage());
            topicFeaturesSummaryResponse.error(e.getMessage());
            return topicFeaturesSummaryResponse;
        } catch (Exception e) {
            log.error("traceId:{},aiCombinationService.getTopicFeaturesAndSummary 调用异常： {}", topicFeaturesSummaryRequest.getTraceId(), e.getMessage());
            topicFeaturesSummaryResponse.error("AI组卷:试题标签和试卷总结异常！");
            return topicFeaturesSummaryResponse;
        }
        return topicFeaturesSummaryResponse;
    }

    /**
     * 调AI能力平台查询试卷总结 只支持单套试卷
     *
     * @param topicFeaturesSummaryRequest
     * @param topicFeaturesRequest
     * @param topicFeaturesMap
     * @return
     */
    public TopicFeaturesSummary generateTopicFeaturesSummary(TopicFeaturesSummaryRequest topicFeaturesSummaryRequest, TopicFeaturesRequest topicFeaturesRequest,
                                                             Map<String, List<TopicFeature>> topicFeaturesMap) {
        List<TopicFeaturesSummaryInfo> summaryInfoList = new ArrayList<>();
        List<com.iflytek.edu.aicombination.param.CompositionRecommendResponse> compositionRecommendResponses = topicFeaturesSummaryRequest.getCompositionResult();
        for (com.iflytek.edu.aicombination.param.CompositionRecommendResponse compositionRecommendResponse : compositionRecommendResponses) {
            List<String> nodeIds = compositionRecommendResponse.getOutNodeInfos().stream().map(NodeInfo::getNodeId).collect(Collectors.toList());
            TopicFeaturesSummaryInfo summaryInfo = new TopicFeaturesSummaryInfo();
            //大模型试卷总结 入参内容如果不为空，调能力平台查询试卷总结
            if(StringUtils.isNotEmpty(compositionRecommendResponse.getSummary())){
                summaryInfo.setSummary(aiAbilityService.getSummary(compositionRecommendResponse, topicFeaturesSummaryRequest.getTraceId(), topicFeaturesRequest.getSceneInfo().getUserId()));
            }
            Map<String, List<TopicFeature>> topicFeaturesMap1 = new HashMap<>();

            if (CollectionUtil.isNotEmpty(nodeIds)) {
                for (Map.Entry<String, List<TopicFeature>> entry : topicFeaturesMap.entrySet()) {
                    if (nodeIds.contains(entry.getKey())) {
                        topicFeaturesMap1.put(entry.getKey(), entry.getValue());
                    }
                }
            }
            summaryInfo.setTopicFeaturesMap(topicFeaturesMap1);
            summaryInfoList.add(summaryInfo);
        }

        TopicFeaturesSummary topicFeaturesSummary = new TopicFeaturesSummary();
        topicFeaturesSummary.setTopicFeaturesSummaryInfoList(summaryInfoList);

        return topicFeaturesSummary;
    }

    /**
     * 把推卷记录放mongo表
     *
     * @param aiManyCombinationRequest
     * @param compositionResult
     */
    private void createLogRecordEntity(AiManyCombinationRequest aiManyCombinationRequest, List<com.iflytek.edu.aicombination.param.CompositionRecommendResponse> compositionResult) {

        List<String> topicIds = new ArrayList<>();
        for (com.iflytek.edu.aicombination.param.CompositionRecommendResponse compositionRecommendResponse : compositionResult) {
            List<NodeInfo> outNodeInfo = compositionRecommendResponse.getOutNodeInfos();
            topicIds.addAll(outNodeInfo.stream().map(NodeInfo::getNodeId).collect(Collectors.toList()));
        }
        log.info("traceId:{},aiCombinationService.createLogRecordEntity 把推卷记录放mongo表:{}", aiManyCombinationRequest.getTraceId(),JSON.toJSONString(topicIds));
        //把推卷记录放mongo表
        if (CollectionUtil.isNotEmpty(topicIds)){
            AiCombinationLogRecordEntity entity = new AiCombinationLogRecordEntity();
            entity.setSessionId(aiManyCombinationRequest.getSessionId());
            entity.setTopicId(topicIds);
            entity.setTraceId(aiManyCombinationRequest.getTraceId());
            entity.setPressCode(aiManyCombinationRequest.getSceneInfo().getPressCode());
            entity.setBizCode(aiManyCombinationRequest.getSceneInfo().getBizCode());
            entity.setBookCode(aiManyCombinationRequest.getSceneInfo().getBookCode());
            entity.setGraphVersion(aiManyCombinationRequest.getSceneInfo().getGraphVersion());
            entity.setStudyCode(aiManyCombinationRequest.getSceneInfo().getStudyCode());
            entity.setSubjectCode(aiManyCombinationRequest.getSceneInfo().getSubjectCode());
            entity.setUserId(aiManyCombinationRequest.getSceneInfo().getUserId());
            entity.setPhaseCode(aiManyCombinationRequest.getSceneInfo().getPhaseCode());
            aiCombinationLogRecordRepository.insert(entity);
        }

    }

}
