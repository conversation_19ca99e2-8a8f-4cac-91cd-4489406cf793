package com.iflytek.edu.aicombination.ability.base;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * AI能力平台 :base参数说明
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AbilityBase implements Serializable {

    private static final long serialVersionUID = 5704964678860381684L;

    /**
     * 链路id，用于追踪日志
     */
    private String traceId;
    /**
     * 应用id
     */
    private String appId;
    /**
     * 鉴权token
     */
    private String authorization;
    /**
     * 版本信息
     */
    private String version;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 用户id
     */
    private String userId;
    /**
     * 产品id
     */
    private String productId;


}
