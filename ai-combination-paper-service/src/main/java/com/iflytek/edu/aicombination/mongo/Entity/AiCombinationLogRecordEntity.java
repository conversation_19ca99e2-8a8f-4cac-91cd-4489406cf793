package com.iflytek.edu.aicombination.mongo.Entity;


import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.Instant;
import java.util.List;

/**
 * AI组卷推题记录document
 *
 * <AUTHOR>
 */
@Data
@Document(collection = "xxj_ai_combination_log_record")
@NoArgsConstructor
public class AiCombinationLogRecordEntity {

    @Id
    private String id;

    /**
     * 会话ID
     */
    @Field(name = "session_id")
    private String sessionId;

    /**
     * 推卷试题ID集合
     */
    @Field(name = "topic_id")
    private List<String> topicId;

    /**
     * 用户id
     */
    @Field(name = "user_id")
    private String userId;
    /**
     * 业务编码
     */
    @Field(name = "biz_code")
    private String bizCode;
    /**
     * 学习场景
     */
    @Field(name = "study_code")
    private String studyCode;
    /**
     * 学习能力
     */
    @Field(name = "study_action")
    private String studyAction;
    /**
     * 图谱版本
     */
    @Field(name = "graph_version")
    private String graphVersion;
    /**
     * 学科
     */
    @Field(name = "subject_code")
    private String subjectCode;
    /**
     * 学段
     */
    @Field(name = "phase_code")
    private String phaseCode;
    /**
     * 教材版本编码
     */
    @Field(name = "book_code")
    private String bookCode;
    /**
     * 行为编码
     */
    @Field(name = "action_code")
    private String pressCode;

    /**
     * 关联的追踪ID
     */
    @Field(name = "trace_id")
    private String traceId;
    /**
     * 创建时间
     */
    @Field(name = "create_time")
    private Instant createTime;

}