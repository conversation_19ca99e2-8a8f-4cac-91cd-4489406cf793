package com.iflytek.edu.aicombination.service.impl;

import com.iflytek.rec.composition.interfaces.param.CompositionRecommendRequest;
import com.iflytek.rec.composition.interfaces.param.CompositionRes;
import com.iflytek.rec.composition.interfaces.param.TopicFeaturesRequest;

/**
 * <AUTHOR>
 */
public interface EngineService {

    public CompositionRes recommend(CompositionRecommendRequest compositionRecommendRequest);

    CompositionRes manyAiCombination(CompositionRecommendRequest compositionRecommendRequest);

    com.iflytek.rec.composition.interfaces.param.WeakPointsResponse weakPointsInfo(com.iflytek.rec.composition.interfaces.param.WeakPointsRequest weakPointsRequestAdapter);

    com.iflytek.rec.composition.interfaces.param.TopicFeaturesResponse topicFeatures(TopicFeaturesRequest topicFeaturesRequest);
}
