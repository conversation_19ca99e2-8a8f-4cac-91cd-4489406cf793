package com.iflytek.edu.aicombination.sentinel;

import com.alibaba.csp.sentinel.SphU;
import com.alibaba.csp.sentinel.slots.block.BlockException;
import com.iflytek.edu.aicombination.constant.AICombinationConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class AiPlatformHealthIndicator implements HealthIndicator {

    @Override
    public Health health() {
        // 检查熔断器状态
        boolean isCircuitBreakerOpen = checkCircuitBreakerStatus();

        if (isCircuitBreakerOpen) {
            return Health.down()
                    .withDetail("circuit_breaker", "OPEN")
                    .withDetail("message", "AI能力平台试卷总结接口熔断中")
                    .build();
        }

        return Health.up().build();
    }

    /**
     * Sentinel 1.8.6 熔断状态检查核心实现
     */
    private boolean checkCircuitBreakerStatus() {

        // 通过模拟请求触发检查（兼容性更强）
        try {
            //如果被熔断会抛出BlockException
            com.alibaba.csp.sentinel.Entry entry = SphU.entry(AICombinationConstant.SUMMARY_RESOURCE);
            entry.exit();
            return false;
        } catch (BlockException ex) {
            // 触发熔断
            return true;
        } catch (Exception e) {
            // 其他异常不计入熔断
            return false;
        }
    }
}
