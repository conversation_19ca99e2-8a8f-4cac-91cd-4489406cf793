package com.iflytek.edu.aicombination.ability.constant;


/**
 *  AI能力平台 内容类型 0业务自定义消息体 1文字 2图片 3音频 4视频 5文件 6含文本代码
 * <AUTHOR>
 */

public enum ContentTypeEnum {
    CUSTOMIZE(0, "业务自定义消息体"),
    CHARACTERS(1, "文字"),
    PICTURE(2, "图片"),
    AUDIO(3, "音频"),
    VIDEO(4, "视频"),
    FILE(5, "文件"),
    INCLUDING_TEXT_CODE(6, "含文本代码");

    private int code;
    private String message;
    ContentTypeEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }
}
