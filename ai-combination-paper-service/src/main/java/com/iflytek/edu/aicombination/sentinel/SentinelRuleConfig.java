package com.iflytek.edu.aicombination.sentinel;

import com.alibaba.csp.sentinel.slots.block.RuleConstant;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRule;
import com.alibaba.csp.sentinel.slots.block.degrade.DegradeRuleManager;
import com.iflytek.edu.aicombination.constant.AICombinationConstant;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Component
public class SentinelRuleConfig implements InitializingBean {

    @Override
    public void afterPropertiesSet() throws Exception {
        initDegradeRule();
    }

    @Value("${sentinel.rule.config.exception.count}")
    private double exceptionCount;

    @Value("${sentinel.rule.config.exception.time.window}")
    private int exceptionTimeWindow;

    @Value("${sentinel.rule.config.exception.min.request.amount}")
    private int exceptionInRequestAmount;

    @Value("${sentinel.rule.config.exception.stat.interval.ms}")
    private int exceptionStatIntervalMs;

    @Value("${sentinel.rule.config.slow.count}")
    private double slowCount;

    @Value("${sentinel.rule.config.slow.time.window}")
    private int slowTimeWindow;

    @Value("${sentinel.rule.config.slow.min.request.amount}")
    private int slowMinRequestAmount;

    @Value("${sentinel.rule.config.slow.ratio.threshold}")
    private double slowRatioThreshold;

    @Value("${sentinel.rule.config.slow.stat.interval.ms}")
    private int slowStatIntervalMs;

    /**
     * 配置AI能力平台接口熔断规则
     */
    private void initDegradeRule() {
        List<DegradeRule> rules = new ArrayList<>();

        // 1.按异常比例熔断
        DegradeRule rule = new DegradeRule();
        // 资源名，与@SentinelResource注解的value一致
        rule.setResource(AICombinationConstant.SUMMARY_RESOURCE);
        rule.setGrade(RuleConstant.DEGRADE_GRADE_EXCEPTION_RATIO);
        // 异常比例阈值50%
        rule.setCount(exceptionCount);
        // 熔断时长10秒
        rule.setTimeWindow(exceptionTimeWindow);
        // 最小请求数5
        rule.setMinRequestAmount(exceptionInRequestAmount);
        // 统计窗口60秒
        rule.setStatIntervalMs(exceptionStatIntervalMs);

        rules.add(rule);

        // 2. 按平均 RT 熔断
        DegradeRule rtRule = new DegradeRule();
        rtRule.setResource(AICombinationConstant.SUMMARY_RESOURCE);
        // 按响应时间
        rtRule.setGrade(RuleConstant.DEGRADE_GRADE_RT) ;
        // 平均 RT > 5000 ms 触发
        rtRule.setCount(slowCount) ;
        // 熔断时长10秒
        rtRule.setTimeWindow(slowTimeWindow);
        // 统计窗口内最小请求数
        rtRule.setMinRequestAmount(slowMinRequestAmount);
        // 慢调用比例阈值50%
        rtRule.setSlowRatioThreshold(slowRatioThreshold);
        // 统计窗口60秒
        rtRule.setStatIntervalMs(slowStatIntervalMs);
        rules.add(rtRule);

        DegradeRuleManager.loadRules(rules);
    }
}
