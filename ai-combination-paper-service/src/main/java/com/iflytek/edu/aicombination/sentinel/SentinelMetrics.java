package com.iflytek.edu.aicombination.sentinel;

import com.alibaba.csp.sentinel.slots.block.degrade.circuitbreaker.EventObserverRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;


/**
 * Sentinel熔断器指标监控组件
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class SentinelMetrics {
    @Autowired
    private MeterRegistry meterRegistry;

    @PostConstruct
    public void registerObserver() {
        EventObserverRegistry.getInstance()
                .addStateChangeObserver("metrics",
                        (prev, now, rule, snapshot) -> {
                            meterRegistry.counter("circuit_breaker_state_change",
                                    "resource", rule.getResource(),
                                    "state", now.name()).increment();
                        });
    }
}