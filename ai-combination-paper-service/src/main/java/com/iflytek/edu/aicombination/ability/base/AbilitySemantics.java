package com.iflytek.edu.aicombination.ability.base;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AbilitySemantics implements Serializable {

    private static final long serialVersionUID = -183580753363551142L;

    private String function;

    private String plugin;

    private List<AbilityPluginArgs> pluginArgs;
}
