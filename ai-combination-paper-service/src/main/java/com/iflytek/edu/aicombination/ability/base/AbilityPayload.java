package com.iflytek.edu.aicombination.ability.base;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Map;

/**
 * AI能力平台 :请求具体内容
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AbilityPayload implements Serializable {

    private static final long serialVersionUID = -4005358738611269526L;
    /**
     * 会话id，通常为uuid
     */
    private String chatId;
    /**
     * 对话id，通常为uuid
     */
    private String dialogId;

    /**
     * 消息id，通常为uuid
     */
    private String messageId;

    /**
     * 用户输入内容
     */
    private String content;
    /**
     * 内容类型 0业务自定义消息体 1文字 2图片 3音频 4视频 5文件 6含文本代码
     */
    private int contentType;
    /**
     *  是否包含上下文 0不包含，1包含
     */
    private int includeContext;
    /**
     * 图片参数
     */
    private Object imgParam;
    /**
     * 音频参数
     */
    private Object audioParam;

    /**
     * 引擎参数
     */
    private Map<String, String> engineParam;


    /**
     * 扩展字段，json格式
     */
    private String extra;


    /**
     * 业务自传上下文信息
     */
    private Object history;

    /**
     * 是否为重新生成消息 默认为false 不是
     */
    private boolean repeatFlag;

    /**
     * 是否需要执行技能 true表示此次调用只需要走技能规划，不走技能执行 默认为false
     */
    private boolean plannerFlag;


}
