package com.iflytek.edu.aicombination.ability;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.csp.sentinel.annotation.SentinelResource;
import com.alibaba.fastjson2.JSON;
import com.iflytek.edu.aicombination.ability.base.AbilityBase;
import com.iflytek.edu.aicombination.ability.base.AbilityContent;
import com.iflytek.edu.aicombination.ability.base.AbilityPayload;
import com.iflytek.edu.aicombination.ability.base.AbilitySceneInfo;
import com.iflytek.edu.aicombination.ability.base.AuthBase;
import com.iflytek.edu.aicombination.ability.constant.Constant;
import com.iflytek.edu.aicombination.ability.constant.ContentTypeEnum;
import com.iflytek.edu.aicombination.ability.constant.IncludeContextEnum;
import com.iflytek.edu.aicombination.ability.request.AuthRequest;
import com.iflytek.edu.aicombination.ability.request.SummaryRequest;
import com.iflytek.edu.aicombination.ability.response.AbilityResponse;
import com.iflytek.edu.aicombination.ability.response.AuthResponse;
import com.iflytek.edu.aicombination.ability.util.HttpUtil;
import com.iflytek.edu.aicombination.ability.util.Md5Util;
import com.iflytek.edu.aicombination.annotation.ServiceRequestMetrics;
import com.iflytek.edu.aicombination.constant.AICombinationConstant;
import com.iflytek.edu.aicombination.param.CompositionRecommendResponse;
import com.iflytek.edu.aicombination.sentinel.AiAbilityServiceBlockHandler;
import com.iflytek.edu.aicombination.utils.XxjMd5Util;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;


import java.util.HashMap;
import java.util.Map;

/**
 * AI 能力平台接口调用
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class AiAbilityServiceImpl implements AiAbilityService {

    @Value("${ai.ability.appId}")
    private String appId;

    @Value("${ai.ability.appKey}")
    private String appKey;

    @Value("${ai.ability.token.url}")
    private String tokenUrl;

    @Value("${ai.ability.summary.url}")
    private String summaryUrl;

    @Value("${ai.ability.version}")
    private String version;

    @Value("${ai.ability.scene.code}")
    private String sceneCode;

    @Value("${ai.ability.intention.code}")
    private String intentionCode;


    /**
     * 获取token
     *
     * @return
     */
    private String getToken() {

        String timeStamp = String.valueOf(System.currentTimeMillis());
        //请求体
        AuthBase base = new AuthBase();
        base.setAppId(appId);
        base.setTimestamp(timeStamp);
        AuthRequest authRequest = new AuthRequest();
        authRequest.setBase(base);

        //获得Authorization
        String sk = Md5Util.getMD5(appKey + timeStamp);
        String requestBodyStr = JSON.toJSONString(authRequest);
        String sign = Md5Util.getMD5(sk + requestBodyStr);

        //添加头
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("Authorization", sign);

        AuthResponse authResponse = null;
        try {
            log.info("调用AI能力平台getToken.authRequest = " + JSON.toJSONString(authRequest));
            String responseBody = HttpUtil.doPost(tokenUrl, headerMap, authRequest);
            authResponse = JSON.parseObject(responseBody, AuthResponse.class);
            log.info("调用AI能力平台getToken.authResponse = " + JSON.toJSONString(authResponse));
        } catch (Exception e) {
            log.error("调用AI能力平台getToken失败" + e.getMessage());
            return "";
        }
        if (Constant.GET_TOKEN_RET_SUCCESS_CODE.equals(authResponse.getRetCode())){
            return  authResponse.getAccessToken();
        }
        return "";
    }

    /**
     * 调用AI能力平台查询试卷总结
     *
     * @param compositionRecommendResponse
     * @param traceId
     * @param userId
     * @return
     */
    @Override
    @ServiceRequestMetrics(desc = "试卷总结AI能力平台接口", type = "ability")
    @SentinelResource(
            value = AICombinationConstant.SUMMARY_RESOURCE,
            blockHandler = "handleSummaryBlock",  // 熔断降级处理方法
            fallback = "handleSummaryFallback",   // 异常降级处理方法
            blockHandlerClass = {AiAbilityServiceBlockHandler.class}
    )
    public String getSummary(CompositionRecommendResponse compositionRecommendResponse, String traceId, String userId) {
        long l = System.currentTimeMillis();

        SummaryRequest summaryRequest = new SummaryRequest();

        AbilityBase abilityBase = new AbilityBase();
        abilityBase.setTraceId(traceId);
        //组装参数
        abilityBase.setVersion(version);
        //AI能力平台userId有32位长度限制，需要按学习机md5加密处理后传
        abilityBase.setUserId(XxjMd5Util.md5Encode(userId));
        AbilitySceneInfo abilitySceneInfo = new AbilitySceneInfo();
        abilitySceneInfo.setSceneCode(sceneCode);
        abilitySceneInfo.setIntentionCode(intentionCode);
        AbilityPayload abilityPayload = new AbilityPayload();
        abilityPayload.setChatId(UUID.randomUUID().toString());
        abilityPayload.setDialogId(UUID.randomUUID().toString());
        abilityPayload.setMessageId(UUID.randomUUID().toString());
        abilityPayload.setContent(compositionRecommendResponse.getSummary());
        abilityPayload.setContentType(ContentTypeEnum.CHARACTERS.getCode());
        abilityPayload.setIncludeContext(IncludeContextEnum.YES.getCode());

        summaryRequest.setBase(abilityBase);
        summaryRequest.setSceneInfo(abilitySceneInfo);
        summaryRequest.setPayload(abilityPayload);
        //添加头
        String summary = "";
        String token = getToken();
        if (StringUtils.isEmpty(token)){
            return summary;
        }
        Map<String, String> headerMap = new HashMap<>();
        headerMap.put("appId", appId);
        headerMap.put("Authorization", token);
        headerMap.put("Content-Type", "application/json");
        AbilityResponse abilityResponse = null;

        try {
            log.info("调用AI能力平台查询试卷总结：getSummary.authRequest = " + JSON.toJSONString(summaryRequest));
            String responseBody = HttpUtil.doPost(summaryUrl, headerMap, summaryRequest);
            abilityResponse = JSON.parseObject(responseBody, AbilityResponse.class);
            log.info("调用AI能力平台查询试卷总结：getSummary.authResponse = " + JSON.toJSONString(abilityResponse));
        } catch (Exception e) {
            log.error("调用AI能力平台查询试卷总结：getSummary失败" + e.getMessage());
            return summary;
        }

        log.info("traceId:{},AiAbilityServiceImpl.getSummary  AI能力平台接口调用 cost time:{}", traceId, System.currentTimeMillis() - l);
        if (ObjectUtil.isNotEmpty(abilityResponse) && Constant.GET_SUMMARY_SUCCESS_CODE.equals(abilityResponse.getCode())) {
            AbilityContent abilityContent = abilityResponse.getData();
            //bizCode 为0,成功有响应，别的直接返回空
            if (ObjectUtil.isNotEmpty(abilityContent) && Constant.GET_SUMMARY_SUCCESS_CODE.equals(abilityContent.getBizCode())) {
                summary = abilityContent.getContent();
            }
        }
        return summary;
    }



}
