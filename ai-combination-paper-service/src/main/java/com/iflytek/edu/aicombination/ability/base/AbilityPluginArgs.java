package com.iflytek.edu.aicombination.ability.base;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AbilityPluginArgs implements Serializable {

    private static final long serialVersionUID = -1353106693109312986L;

    private String domain;
    private String edition;
    private String grade;
    private String anchor;
    private String book;
    private String catalog;
    private String count;
    private String type;
    private String difficulty;
    private String others;
    private String range;
    private String require;
}
