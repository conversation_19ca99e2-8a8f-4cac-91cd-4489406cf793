package com.iflytek.edu.aicombination.utils;

import com.iflytek.edu.aicombination.param.AiCombinationRequest;
import com.iflytek.edu.aicombination.param.AiManyCombinationRequest;
import com.iflytek.edu.aicombination.param.TopicFeaturesSummaryRequest;
import com.iflytek.edu.aicombination.param.WeakPointsRequest;
import com.iflytek.skyline.common.exception.ParamInvalidException;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Iterator;
import java.util.Set;


public class ValidateUtil {

    private static ValidatorFactory validatorFactory = Validation.buildDefaultValidatorFactory();
    private static Validator validator = ValidateUtil.validatorFactory.getValidator();

    /**
     * AI组卷 业务参数校验
     * @param request
     */
    public static void validatorScence(AiCombinationRequest request) {
        validate(request);
        validate(request.getSceneInfo());
    }
    /**
     * AI组卷 业务参数校验
     * @param request
     */
    public static void validatorScenceManyAiCombination(AiManyCombinationRequest request) {
        validate(request);
        validate(request.getSceneInfo());
    }

    /**
     * AI组卷：查询用户书下的薄弱点个数 业务参数校验
     * @param request
     */
    public static void validatorWeakPoints(WeakPointsRequest request) {
        validate(request);
        validate(request.getSceneInfo());
    }

    /**
     * AI组卷：试题标签和试卷总结 业务参数校验
     * @param request
     */
    public static void validatorTopicFeaturesSummary(TopicFeaturesSummaryRequest request) {
        validate(request);
        validate(request.getSceneInfo());
    }


    public static synchronized <T> void validate(T o) {
        Set<ConstraintViolation<T>> constraintViolationSet = ValidateUtil.validator.validate(o);
        if (!constraintViolationSet.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            Iterator<ConstraintViolation<T>> it = constraintViolationSet.iterator();
            while (it.hasNext()) {
                ConstraintViolation<T> constraintViolation = it.next();
                sb.append(constraintViolation.getMessage());
                if (it.hasNext()) {
                    sb.append(";");
                }
            }
            throw new ParamInvalidException(sb.toString());
        }
    }
}
