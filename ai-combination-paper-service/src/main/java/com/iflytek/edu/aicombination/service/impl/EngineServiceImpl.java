package com.iflytek.edu.aicombination.service.impl;

import com.iflytek.edu.aicombination.annotation.ServiceRequestMetrics;
import com.iflytek.rec.composition.ICompositonRecommend;
import com.iflytek.rec.composition.interfaces.impl.CompositionRecommendImpl;
import com.iflytek.rec.composition.interfaces.param.CompositionRecommendRequest;
import com.iflytek.rec.composition.interfaces.param.CompositionRes;
import com.iflytek.rec.composition.interfaces.param.TopicFeaturesRequest;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class EngineServiceImpl implements EngineService {

    private static final ICompositonRecommend compositonRecommend = new CompositionRecommendImpl();

    @ServiceRequestMetrics(desc = "组单套卷引擎接口", type = "engine")
    @Override
    public CompositionRes recommend(CompositionRecommendRequest compositionRecommendRequest) {
        return compositonRecommend.recommend(compositionRecommendRequest);
    }

    @ServiceRequestMetrics(desc = "组多套卷引擎接口", type = "engine")
    @Override
    public CompositionRes manyAiCombination(CompositionRecommendRequest compositionRecommendRequest) {
        return compositonRecommend.recommend(compositionRecommendRequest);
    }

    @ServiceRequestMetrics(desc = "查询用户书下的薄弱点个数引擎接口", type = "engine")
    @Override
    public com.iflytek.rec.composition.interfaces.param.WeakPointsResponse weakPointsInfo(com.iflytek.rec.composition.interfaces.param.WeakPointsRequest weakPointsRequestAdapter) {
        return compositonRecommend.weakPointsInfo(weakPointsRequestAdapter);
    }

    @ServiceRequestMetrics(desc = "试题标签引擎接口", type = "engine")
    @Override
    public com.iflytek.rec.composition.interfaces.param.TopicFeaturesResponse topicFeatures(TopicFeaturesRequest topicFeaturesRequest) {
        return compositonRecommend.topicFeatures(topicFeaturesRequest);
    }
}
