#-----------------------------------------------------------------
server.port=22330
spring.application.name=ai-combination-paper
ai.combination.graph-version=20240527_003
#-----------------------------------------------------------------
skyline.data.api.sdk.keep-alive-duration-second=5
skyline.data.api.sdk.max-idle-conn=5
skyline.data.api.sdk.timeout-ms=5000
skyline.data.api.sdk.timeoutMs=5000
skyline.data.api.sdk.maxIdleConn=10
skyline.data.api.sdk.queryLabelDataApiId=api-me0vg2e4
skyline.data.api.sdk.queryApiDataApiId=api-2rl8e4vh
#-----------------------------------------------------------------
#mongodb
logging.level.com.iflytek.skylab=INFO
#-----------------------------------------------------------------
skyline.data.api.sdk.app-key=app-69v3u6vj
skyline.data.api.sdk.app-secret=09bb950d5315a56305b92c558be03e4b8fcd1dcd
skyline.data.api.sdk.url=http://172.31.162.225:30890/api/v1/execute
skyline.data.api.sdk.keepAliveDurationSecond=5
#-----------------------------------------------------------------
skylab.zion.dict-table-name=dim_xxj_dic_model
skylab.zion.dict-family=u
skylab.zion.dict-qualifier=dicModel
skylab.zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
skylab.zion.dict-refresh-period-seconds=3600
skylab.zion.dict-data-api-item.dataApiId=api-vimqibeu
skylab.zion.dict-data-api-item.version=1
skylab.zion.cache-max-size=1000000
skylab.zion.feature-data-api-item.dataApiId=api-x4znzm0l
skylab.zion.feature-data-api-item.version=3
skylab.data.api.study-log.featureVersion=1
skylab.data.api.study-log.graphVersion=v2022-03
#-----------------------------------------------------------------
skylab.data.api.graph.hosts=*************:9669
skylab.data.api.graph.username=root
skylab.data.api.graph.nodeProps.ANCHOR_POINT=anchorPointType,difficulty,examPoint,phaseCode,realLastLevelRelationCatas,subjectCode,falliblePoint,name
skylab.data.api.graph.nodeProps.CHECK_POINT=
skylab.data.api.graph.nodeProps.LEARN_PATH=
#------------------------epas-dubbo------------------
dubbo.service-port=26009
application.epas.appKey=ai-combination-paper
application.epas.appSecret=a8e437a939283057
application.epas.addrServerUrl=http://pre.epas.changyan.com/address

#-----------------Prometheus Metrics-------------------
management.metrics.export.prometheus.enabled=true
management.endpoints.web.exposure.include=prometheus,health,info
management.endpoint.health.show-details=always
management.endpoint.prometheus.enabled=true