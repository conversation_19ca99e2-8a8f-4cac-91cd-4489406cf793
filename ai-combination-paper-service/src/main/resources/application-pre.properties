#-----------------------------------------------------------------
server.port=22330
spring.application.name=ai-combination-paper
ai.combination.graph-version=20240527_003
#-----------------------------------------------------------------
skyline.data.api.sdk.app-key=app-ywqysanx
skyline.data.api.sdk.app-secret=3e9e317892f039a6a15ad4316bcf6123ac9a7074
skyline.data.api.sdk.url=http://10.100.104.142:30890/api/v1/execute
skyline.data.api.sdk.keepAliveDurationSecond=5
#-----------------------------------------------------------------
skylab.zion.dict-table-name=dim_xxj_dic_model
skylab.zion.dict-family=u
skylab.zion.dict-qualifier=dicModel
skylab.zion.dict-default-row-key=xxj.feature.dic.model#v2022-03
skylab.zion.dict-refresh-period-seconds=10
skylab.zion.dict-data-api-item.dataApiId=api-m7iaotyx
#设置为空
skylab.zion.dict-data-api-item.version=
skylab.zion.post-process-fea-names=user_level
skylab.zion.cache-exclude-fea-names=user_level,anchor_similaryuser_mastery,check_similaryuser_mastery
skylab.zion.cache-max-size=1000000
skylab.zion.query-timeout=2000
skylab.zion.feature-data-api-item.dataApiId=api-0ufyxwfz
#设置为空
skylab.zion.feature-data-api-item.version=
skylab.data.api.study-log.featureVersion=1
skylab.data.api.study-log.graphVersion=v2022-03
#-----------------------------------------------------------------
#-----------------------------------------------------------------
skylab.data.api.graph.hosts=*************:9669,*************:9669,************:9669
skylab.data.api.graph.username=root
skylab.data.api.graph.maxConnSize=1000
#-----------------------------------------------------------------
#mongodb
logging.level.com.iflytek.skylab=INFO
skylab.data.api.graph.nodeProps.ANCHOR_POINT=anchorPointType,difficulty,examPoint,phaseCode,realLastLevelRelationCatas,subjectCode,falliblePoint,name
skylab.data.api.graph.nodeProps.CHECK_POINT=
skylab.data.api.graph.nodeProps.LEARN_PATH=
#------------------------epas-dubbo------------------
dubbo.service-port=26009
application.epas.appKey=AIZJFW-PRE
application.epas.appSecret=b758f8ca45b582ce
application.epas.addrServerUrl=http://pre.epas.changyan.com/address

#-----------------Prometheus Metrics-------------------
management.metrics.export.prometheus.enabled=true
management.endpoints.web.exposure.include=prometheus,health,info
management.endpoint.health.show-details=always
management.endpoint.prometheus.enabled=true