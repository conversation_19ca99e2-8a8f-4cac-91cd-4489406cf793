<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
            http://www.springframework.org/schema/beans/spring-beans.xsd
            http://code.alibabatech.com/schema/dubbo
            http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:application name="ai-combination-paper"/>

    <!-- epas设置-->
    <dubbo:registry id="epas" protocol="epas" address="epasConfig" register="true"/>

    <bean id="epasConfig" class="com.iflytek.edu.epas.dubbo.config.EpasConfig">
        <property name="appKey" value="${application.epas.appKey}"/>
        <property name="appSecret" value="${application.epas.appSecret}"/>
        <!-- 地址服务url 配置中心下拉使用addrServerUrl,本地调试使用registerUrl-->
        <property name="addrServerUrl" value="${application.epas.addrServerUrl}"/>
    </bean>
    <!-- Dubbo协议 端口号-->
    <dubbo:protocol name="dubbo" port="${dubbo.service-port}"/>

    <!--epas服务注册-->
    <dubbo:service registry="epas"  protocol="dubbo" interface="com.iflytek.edu.aicombination.service.AiCombinationService" ref="aiCombinationService" timeout="1000"/>

</beans>