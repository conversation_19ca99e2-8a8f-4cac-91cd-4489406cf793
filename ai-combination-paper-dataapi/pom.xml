<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.iflytek.edu</groupId>
        <artifactId>ai-combination-paper</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>ai-combination-paper-dataapi</artifactId>
    <packaging>jar</packaging>

    <properties>
        <maven.source.skip>false</maven.source.skip>
        <maven.deploy.skip>false</maven.deploy.skip>

        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <skylab-core-version>2.0.9-SNAPSHOT</skylab-core-version>
        <skipTests>true</skipTests>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.iflytek.skylab</groupId>
            <artifactId>skylab-core-dataapi</artifactId>
            <version>${skylab-core-version}</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>
</project>