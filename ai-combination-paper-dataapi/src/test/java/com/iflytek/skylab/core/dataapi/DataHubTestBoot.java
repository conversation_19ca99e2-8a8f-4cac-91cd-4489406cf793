package com.iflytek.skylab.core.dataapi;

import com.iflytek.edu.aicombination.EnablePaperDataAPI;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import com.iflytek.skylab.core.dataapi.annotation.EnableFeatureAPI;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;

@SpringBootApplication(exclude = {RedisAutoConfiguration.class})
@EnableDataHub
@EnablePaperDataAPI
@EnableFeatureAPI
//@ComponentScan("com.iflytek.edu.aicombination")
@Slf4j
public class DataHubTestBoot {

    public static void main(String[] args) {
        SpringApplication.run(DataHubTestBoot.class, args);
        log.info("================AI组卷服务test启动成功============================");

    }
}
