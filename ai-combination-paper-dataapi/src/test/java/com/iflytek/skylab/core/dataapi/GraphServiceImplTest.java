package com.iflytek.skylab.core.dataapi;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.edu.aicombination.cache.GraphCache;
import com.iflytek.skylab.core.constant.GraphVertexType;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.GraphLabelQuery;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.service.cache.LocalFileGraphCache;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT, classes = DataHubTestBoot.class)
public class GraphServiceImplTest {
    @Autowired
    LocalFileGraphCache diagGraphCache;
    //教材->书本缓存
    ConcurrentHashMap<String, ConcurrentHashSet<String>> pressBook = new ConcurrentHashMap<>();
    ConcurrentHashMap<String, ConcurrentHashSet<String>> bookCata = new ConcurrentHashMap<>();
    ConcurrentHashMap<String, GraphData> cataPoint = new ConcurrentHashMap<>();

    String graphVersion = "20250402_001";

    @Test
    public void getGraphByPress() {
//        diagGraphCache.initGraph();
        GraphData graphByPress = GraphCache.getGraphByPress("01");

        graphByPress.getVertices().forEach(item -> {

            if (item.getLabel().equals("PRESS")) {
                System.out.println("PRESS item = " + item);
            }
            if (item.getLabel().equals("BOOK")) {
                System.out.println("BOOK item = " + item);
            }

        });
        System.err.println();
        System.err.println();
        graphByPress.getEdges().forEach(item -> {

                    if (item.getLabel().equals("PRESS_BOOK")) {
                        System.out.println("PRESS_BOOK item = " + item);
                    }
                    if (item.getLabel().equals("BOOK_UNIT")) {
                        System.out.println("BOOK_UNIT item = " + item);
                    }

                }

        );

        System.err.println();
    }


    /**
     * 测试查询子图2。
     *
     * @throws Exception 如果查询过程中发生错误，抛出异常。
     */
    @Test
    public void cache() {
        long start = System.currentTimeMillis();

        List<String[]> allowSubjectPhase = Lists.newArrayList();
        //小数
        allowSubjectPhase.add(new String[]{"03", "02"});
        //查询所有UNIT
        List<String> points = new ArrayList<>();
        for (String[] allow : allowSubjectPhase) {
            //学科学段过滤
            JSONObject props = new JSONObject().fluentPut("phase", allow[0]).fluentPut("subject", allow[1]);
            GraphData graphData = DataHub.getGraphService().lookup(new GraphLabelQuery().setGraphVersion(graphVersion).setTraceId(IdUtil.fastSimpleUUID()).setLabel(GraphVertexType.UNIT), props);
            points.addAll(graphData.getVertices().stream().map(GraphData.GraphVertex::getId).collect(Collectors.toList()));
        }


        int i = 0;
        for (String cata : points) {
            log.info("加载缓存{}：{}/{}", cata, i++, points.size());
            //截取
            List<String> split = StrUtil.split(cata, "_");
            String press = split.get(0);
            String book = split.get(0) + "_" + split.get(1);
//            press  -> book 缓存
            if (!pressBook.containsKey(press)) {
                pressBook.put(press, new ConcurrentHashSet<>());
            }
            pressBook.get(press).add(book);

//            book  -> cata 缓存
            if (!bookCata.containsKey(book)) {
                bookCata.put(book, new ConcurrentHashSet<>());
            }
            bookCata.get(book).add(cata);
//            cata  -> anchor-> topic 缓存
            GraphData data = query(graphVersion, cata);

            cataPoint.put(cata, data);

        }
        pressBook.forEach((press, books) -> {
            log.info("缓存到 press->books = {}->{}", press, books);
        });
        bookCata.forEach((books, cata) -> {
            log.info("缓存到 books->cata = {}->{}", books, cata);
        });
        cataPoint.forEach((cata, graphData) -> {
            log.info("缓存到 cata->data = {}->[边={},点={}]", cata, graphData.getEdges().size(), graphData.getVertices().size());
        });
        log.info("图谱缓存结束,耗时：{}", System.currentTimeMillis() - start);


        System.err.println();
    }

    @After
    public void after() {

        pressBook.forEach((press, books) -> {
            log.info("缓存到 press->books = {}->{}", press, books);
            GraphData graphByPress = getGraphByPress(press);
            log.info("press={},查询到图形数据：{},{}", press, graphByPress.getEdges().size(), graphByPress.getVertices().size());
            books.forEach(book -> {
                log.info("缓存到 book={}", book);
                GraphData graphByBook = getGraphByBook(Arrays.asList(book));
                log.info("press={},查询到图形数据：{},{}", book, graphByBook.getEdges().size(), graphByBook.getVertices().size());

            });
        });
    }

    /**
     * 根据出版社代码获取图形数据。
     *
     * @param pressCode 需要查询的出版社代码。
     * @return 如果出版社存在，返回该出版社下所有书籍的图形数据；否则返回null。
     */
    public GraphData getGraphByPress(String pressCode) {
        ConcurrentHashSet<String> books = pressBook.get(pressCode);
        if (books != null) {
            /**
             * 根据书籍代码获取图形数据。
             * @param bookCode 需要查询的书籍代码列表。
             * @return 返回一个GraphData对象，其中包含了所有书籍代码对应的顶点和边的信息。如果书籍代码不存在或者没有对应的顶点或边，则返回的GraphData对象的相应集合为空。
             */
            return getGraphByBook(new ArrayList<>(books));
        }
        return null;
    }

    /**
     * 根据书籍代码获取图形数据。
     *
     * @param bookCode 需要查询的书籍代码列表。
     * @return 返回一个GraphData对象，其中包含了所有书籍代码对应的顶点和边的信息。如果书籍代码不存在或者没有对应的顶点或边，则返回的GraphData对象的相应集合为空。
     */
    public GraphData getGraphByBook(List<String> bookCode) {
        GraphData graphData = new GraphData();
        graphData.setVertices(new ArrayList<>());
        graphData.setEdges(new ArrayList<>());
        bookCode.forEach(book -> {
            if (bookCata.containsKey(book)) {
                for (String cata : bookCata.get(book)) {
                    if (cataPoint.containsKey(cata)) {
                        GraphData graphData2 = cataPoint.get(cata);
                        if (graphData2 != null) {
                            if (CollUtil.isNotEmpty(graphData2.getVertices())) {
                                graphData.getVertices().addAll(graphData2.getVertices());
                            }
                            if (CollUtil.isNotEmpty(graphData2.getEdges())) {
                                graphData.getEdges().addAll(graphData2.getEdges());
                            }
                        }

                    }
                }
            }
        });
        return graphData;
    }

    /**
     * 根据类别获取图形数据。
     *
     * @param cata 需要查询的类别。
     * @return 如果存在对应类别的图形数据，返回该数据；否则返回null。
     */
    public GraphData getGraphByCata(String cata) {
        return cataPoint.get(cata);
    }

    @Deprecated
    public GraphData query2(String graphVersion, String unitCode) {

        long currentTimeMillis = System.currentTimeMillis();
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion(graphVersion);
        subGraphQuery.setRootVertexLabel("UNIT");
        subGraphQuery.setRootVertexIdList(Arrays.asList(unitCode));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("UNIT").target("ANCHOR_POINT").build());
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source("ANCHOR_POINT").target("TOPIC").build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = DataHub.getGraphService().querySubGraphNeedProps(subGraphQuery);
        Assert.assertTrue(graphData != null);
        //移除不必要字段
        graphData.getEdges().forEach(item -> {
            item.setId(null);
            item.setProperties(null);
        });
        return graphData;

    }

    /**
     * 查询图形数据。
     *
     * @param graphVersion 图形版本号。
     * @param unitCode     单位代码。
     * @return 返回查询到的图形数据，包括顶点和边的信息。如果查询结果为空或者不存在，则返回空的图形数据。
     */
    public GraphData query(String graphVersion, String unitCode) {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion(graphVersion);
        subGraphQuery.setRootVertexLabel(GraphVertexType.UNIT);
        subGraphQuery.setRootVertexIdList(Arrays.asList(unitCode));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(GraphVertexType.UNIT).target(GraphVertexType.ANCHOR_POINT).build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = DataHub.getGraphService().querySubGraphNeedProps(subGraphQuery);

        if (CollUtil.isNotEmpty(graphData.getEdges())) {

            List<String> anchorIds = graphData.getEdges().stream().map(GraphData.GraphEdge::getTarget).collect(Collectors.toList());

            SubGraphQuery graphQuery2 = new SubGraphQuery();
            graphQuery2.setTraceId(UUID.randomUUID().toString());
            graphQuery2.setGraphVersion(graphVersion);
            graphQuery2.setRootVertexLabel(GraphVertexType.ANCHOR_POINT);
            graphQuery2.setRootVertexIdList(anchorIds);
            //        只查询边关系
            graphQuery2.setIncludeFields(Arrays.asList("EDGES"));
            List<SubGraphQuery.EdgeLabel> edgeLabels2 = new ArrayList<>();
            edgeLabels2.add(SubGraphQuery.EdgeLabel.builder().source(GraphVertexType.ANCHOR_POINT).target(GraphVertexType.TOPIC).build());
            graphQuery2.setEdgeLabels(edgeLabels2);
            GraphData graphData2 = DataHub.getGraphService().querySubGraph(graphQuery2);
            List<GraphData.GraphEdge> edges = graphData2.getEdges();
            if (CollUtil.isNotEmpty(edges)) {
                //追加边
                graphData.getEdges().addAll(edges);
                //追加topic对象
                List<GraphData.GraphVertex> topics = edges.stream().map(graphEdge -> GraphData.GraphVertex.builder().id(graphEdge.getTarget()).label(GraphVertexType.TOPIC).build()).collect(Collectors.toList());
                graphData.getVertices().addAll(topics);
            }
        }
        //移除不必要字段
        graphData.getEdges().forEach(item -> {
            item.setId(null);
            item.setProperties(null);
        });
        return graphData;
    }

    @Test
    public void compare() {
        long currentTimeMillis = System.currentTimeMillis();
        GraphData query = query("20250219_001", "01_09020101-002_002");
        long currentTimeMillis2 = System.currentTimeMillis();
        GraphData query2 = query2("20250219_001", "01_09020101-002_002");
        long currentTimeMillis3 = System.currentTimeMillis();
        System.out.println("耗时1：" + (currentTimeMillis2 - currentTimeMillis));
        System.out.println("耗时2：" + (currentTimeMillis3 - currentTimeMillis2));
        for (GraphData.GraphVertex vertex : query2.getVertices()) {
            if (vertex.getLabel().equals("TOPIC")) {
                vertex.setProperties(null);
            }
        }
        Assert.assertTrue(JSON.toJSONString(query).length() == JSON.toJSONString(query2).length());
        System.err.println();

    }
}
