package com.iflytek.edu.aicombination.cache;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.iflytek.skylab.core.constant.GraphVertexType;
import com.iflytek.skylab.core.dataapi.data.GraphData;
import com.iflytek.skylab.core.dataapi.data.GraphLabelQuery;
import com.iflytek.skylab.core.dataapi.data.SubGraphQuery;
import com.iflytek.skylab.core.dataapi.service.GraphService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.iflytek.skylab.core.constant.GraphVertexType.*;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName GraphCache
 * @description 图谱缓存
 * @date 2024/5/30 19:17
 */
@Slf4j
public class GraphCache {
    private static final String DEGE_PRESS_BOOK = "PRESS_BOOK";
    private static final String DEGE_BOOK_UNIT = "BOOK_UNIT";
    @Autowired
    private GraphService graphService;

    /**
     * 图谱版本
     */
    @Value("${ai.combination.graph-version}")
    private String graphVersion;

    /**
     * 教材->书本缓存
     */
    protected static final ConcurrentHashMap<String, ConcurrentHashSet<String>> PRESSBOOK_CACHE = new ConcurrentHashMap<>();
    protected static final ConcurrentHashMap<String, ConcurrentHashSet<String>> BOOKCATA_CACHE = new ConcurrentHashMap<>();
    protected static final ConcurrentHashMap<String, GraphData> CATAPOINT_CACHE = new ConcurrentHashMap<>();

    @PostConstruct
    public void initGraph() {
//        bookToUnitToPointCache();
    }

    /**
     * 加载缓存
     */
    private void bookToUnitToPointCache() {
        long start = System.currentTimeMillis();
        List<String[]> allowSubjectPhase = Lists.newArrayList();
        //小数
        allowSubjectPhase.add(new String[]{"03", "02"});
        //初数
        //allowSubjectPhase.add(new String[]{"04", "02"});
        //查询所有UNIT
        List<String> points = new ArrayList<>();
        for (String[] allow : allowSubjectPhase) {
            //学科学段过滤
            JSONObject props = new JSONObject().fluentPut("phase", allow[0]).fluentPut("subject", allow[1]);
            GraphData graphData = graphService.lookup(new GraphLabelQuery().setGraphVersion(graphVersion).setTraceId(IdUtil.fastSimpleUUID())
                    .setLabel(UNIT), props);
            points.addAll(graphData.getVertices().stream().map(GraphData.GraphVertex::getId).collect(Collectors.toList()));
        }

        int i = 0;
        for (String cata : points) {
            log.info("加载缓存进度{}=====：{}/{}", cata, i++, points.size());
            //截取
            List<String> split = StrUtil.split(cata, "_");
            if (CollectionUtils.isEmpty(split) || split.size() < 2) {
                continue;
            }
            String press = split.get(0);
            String book = split.get(0) + "_" + split.get(1);
            //press  -> book 缓存
            if (!PRESSBOOK_CACHE.containsKey(press)) {
                PRESSBOOK_CACHE.put(press, new ConcurrentHashSet<>());
            }
            PRESSBOOK_CACHE.get(press).add(book);
            //book  -> cata 缓存
            if (!BOOKCATA_CACHE.containsKey(book)) {
                BOOKCATA_CACHE.put(book, new ConcurrentHashSet<>());
            }
            BOOKCATA_CACHE.get(book).add(cata);
            //cata  -> anchor-> topic 缓存
            GraphData data = query(graphVersion, cata);
            CATAPOINT_CACHE.put(cata, data);

        }
        PRESSBOOK_CACHE.forEach((press, books) -> {
            log.info("缓存到 press->books = {}->{}", press, books);
        });
        BOOKCATA_CACHE.forEach((books, cata) -> {
            log.info("缓存到 books->cata = {}->{}", books, cata);
        });
        CATAPOINT_CACHE.forEach((cata, graphData) -> {
            log.info("缓存到 cata->data = {}->[边={},点={}]", cata, graphData.getEdges().size(), graphData.getVertices().size());
        });
        log.info("图谱缓存结束,耗时：{}", System.currentTimeMillis() - start);
    }


    /**
     * 版本 press->book->unit->anchor->topic
     * 根据出版社代码获取图形数据。
     *
     * @param pressCode 需要查询的出版社代码。
     * @return 如果出版社存在，返回该出版社下所有书籍的图形数据；否则返回null。
     */
    public static GraphData getGraphByPress(String pressCode) {
        ConcurrentHashSet<String> books = PRESSBOOK_CACHE.get(pressCode);
        if (books != null) {

            GraphData graphByPress = new GraphData();
            graphByPress.setVertices(new ArrayList<>());
            graphByPress.setEdges(new ArrayList<>());

            //封装点信息
            graphByPress.getVertices().add(GraphData.GraphVertex.builder().id(pressCode).label(PRESS).build());
            books.forEach(book -> {
//                graphByPress.getVertices().add(GraphData.GraphVertex.builder().id(book).label(BOOK).build());
                graphByPress.getEdges().add(GraphData.GraphEdge.builder().label(DEGE_PRESS_BOOK).source(pressCode).target(book).build());
            });

            GraphData graphByBook = getGraphByBook(new ArrayList<>(books));

            if (CollUtil.isNotEmpty(graphByBook.getEdges())) {
                graphByPress.getEdges().addAll(graphByBook.getEdges());
            }
            if (CollUtil.isNotEmpty(graphByBook.getVertices())) {
                graphByPress.getVertices().addAll(graphByBook.getVertices());
            }
            return graphByPress;
        }
        return null;
    }

    /**
     * 根据类别获取图形数据。
     *
     * @param cata 需要查询的类别。
     * @return 如果存在对应类别的图形数据，返回该数据；否则返回null。
     */
    public static GraphData getGraphByCata(String cata) {
        return CATAPOINT_CACHE.get(cata);
    }

    /**
     * book->unit->anchor->topic
     * <p>
     * 根据书籍代码获取图形数据。
     *
     * @param bookCode 需要查询的书籍代码列表。
     * @return 返回一个GraphData对象，其中包含了所有书籍代码对应的顶点和边的信息。如果书籍代码不存在或者没有对应的顶点或边，则返回的GraphData对象的相应集合为空。
     */
    public static GraphData getGraphByBook(List<String> bookCode) {
        GraphData graphData = new GraphData();
        graphData.setVertices(new ArrayList<>());
        graphData.setEdges(new ArrayList<>());
        for (String book : bookCode) {

            if (BOOKCATA_CACHE.containsKey(book)) {

                ConcurrentHashSet<String> units = BOOKCATA_CACHE.get(book);
                //封装点信息
                //书本对象去重
                GraphData.GraphVertex bookInfo = GraphData.GraphVertex.builder().id(book).label(BOOK).build();
                graphData.getVertices().add(bookInfo);

                units.forEach(unit -> {
                    graphData.getVertices().add(GraphData.GraphVertex.builder().id(unit).label(UNIT).build());
                    graphData.getEdges().add(GraphData.GraphEdge.builder().label(DEGE_BOOK_UNIT).source(book).target(unit).build());
                });

                //章到点
                units.stream().filter(CATAPOINT_CACHE::containsKey).map(CATAPOINT_CACHE::get).filter(Objects::nonNull).forEach(graphData2 -> {
                    if (CollUtil.isNotEmpty(graphData2.getVertices())) {
                        graphData.getVertices().addAll(graphData2.getVertices());
                    }
                    if (CollUtil.isNotEmpty(graphData2.getEdges())) {
                        graphData.getEdges().addAll(graphData2.getEdges());
                    }
                });
            }

        }
        return graphData;
    }

    /**
     * 查询图形数据。
     *
     * @param graphVersion 图形版本号。
     * @param unitCode     单位代码。
     * @return 返回查询到的图形数据，包括顶点和边的信息。如果查询结果为空或者不存在，则返回空的图形数据。
     */
    private GraphData query(String graphVersion, String unitCode) {
        SubGraphQuery subGraphQuery = new SubGraphQuery();
        subGraphQuery.setTraceId(UUID.randomUUID().toString());
        subGraphQuery.setGraphVersion(graphVersion);
        subGraphQuery.setRootVertexLabel(UNIT);
        subGraphQuery.setRootVertexIdList(Collections.singletonList(unitCode));
        List<SubGraphQuery.EdgeLabel> edgeLabels = new ArrayList<>();
        edgeLabels.add(SubGraphQuery.EdgeLabel.builder().source(UNIT).target(GraphVertexType.ANCHOR_POINT).build());
        subGraphQuery.setEdgeLabels(edgeLabels);
        GraphData graphData = graphService.querySubGraphNeedProps(subGraphQuery);

        if (CollUtil.isNotEmpty(graphData.getEdges())) {

            List<String> anchorIds = graphData.getEdges().stream().map(GraphData.GraphEdge::getTarget).collect(Collectors.toList());

            SubGraphQuery graphQuery2 = new SubGraphQuery();
            graphQuery2.setTraceId(UUID.randomUUID().toString());
            graphQuery2.setGraphVersion(graphVersion);
            graphQuery2.setRootVertexLabel(GraphVertexType.ANCHOR_POINT);
            graphQuery2.setRootVertexIdList(anchorIds);
            //只查询边关系
            graphQuery2.setIncludeFields(Collections.singletonList("EDGES"));
            List<SubGraphQuery.EdgeLabel> edgeLabels2 = new ArrayList<>();
            edgeLabels2.add(SubGraphQuery.EdgeLabel.builder().source(GraphVertexType.ANCHOR_POINT).target(GraphVertexType.TOPIC).build());
            graphQuery2.setEdgeLabels(edgeLabels2);
            GraphData graphData2 = graphService.querySubGraph(graphQuery2);
            List<GraphData.GraphEdge> edges = graphData2.getEdges();
            if (CollUtil.isNotEmpty(edges)) {
                //追加边
                graphData.getEdges().addAll(edges);
                //追加topic对象
                List<GraphData.GraphVertex> topics = edges.stream().map(graphEdge ->
                                GraphData.GraphVertex.builder().id(graphEdge.getTarget()).label(GraphVertexType.TOPIC).build())
                        .collect(Collectors.toList());
                graphData.getVertices().addAll(topics);
            }
        }
        //移除不必要字段
        graphData.getEdges().forEach(item -> {
            item.setId(null);
            item.setProperties(null);
        });
        return graphData;
    }
}
