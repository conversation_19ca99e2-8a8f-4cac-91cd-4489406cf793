package com.iflytek.edu.aicombination.config;

import com.iflytek.edu.aicombination.EnablePaperDataAPI;
import com.iflytek.skylab.core.dataapi.annotation.EnableDataHub;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName AiCombinationConfig
 * @description TODO
 * @date 2024/5/30 19:50
 */
@Configuration
@EnableDataHub
@ConditionalOnBean(annotation = EnablePaperDataAPI.class)
public class AiCombinationConfiguration {

//    @Bean
//    @ConfigurationProperties("skylab.data.api.graph")
//    public GraphProperties graphProperties() {
//        return new GraphProperties();
//    }
//    @Bean
//    public GraphCache diagGraphCache(GraphProperties graphProperties) throws Exception {
//        return new LocalFileGraphCache(graphProperties);
//    }
}
