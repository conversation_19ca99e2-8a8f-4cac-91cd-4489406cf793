package com.iflytek.edu.aicombination.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class QueryParam implements Serializable {

    private static final long serialVersionUID = -1235782480488314249L;

    /**
     * 学段
     */
    private String phaseCode;

    /**
     * 学科
     */
    private String subjectCode;

    /**
     * 查询参数
     */
    private String searchType;

    /**
     * 根节点id列表
     */
    private List<String> rootVertexIdList;
}
