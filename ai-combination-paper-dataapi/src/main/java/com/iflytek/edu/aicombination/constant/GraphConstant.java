package com.iflytek.edu.aicombination.constant;

public class GraphConstant {

    /**
     * 文件格式utf-8
     */
    public static String FILE_FORMAT_UTF8 = "UTF-8";

    /**
     * 图谱边 锚点->题
     */
    public static String EDGE_ANCHOR_POINT_TOPIC = "ANCHOR_POINT_TOPIC";

    /**
     * 图谱边 锚点->考法点
     */
    public static String EDGE_ANCHOR_POINT_EXAM_POINT = "ANCHOR_POINT_EXAM_POINT";

    /**
     * 图谱边 考法点->题
     */
    public static String EDGE_EXAM_POINT_TOPIC = "EXAM_POINT_TOPIC";

    /**
     * 锚点新课标属性
     */
    public static String ANCHOR_CLASS_STANDARD_FEATURES = "classStandardFeatures";

    /**
     * 题点新课标属性
     */
    public static String TOPIC_XKB_CORE_ATTAINMENT = "topic_xkb_core_attainment";

    /**
     * 题点难度属性
     */
    public static String TOPIC_DIFFICULTY = "topic_difficulty";

    /**
     * 题点题型属性
     */
    public static String TOPIC_TYPE = "topic_type";

    /**
     * 考点->题 的边上新课标属性字段
     */
    public static String XKB_LABEL = "xgkLabel";

    /**
     * 新情境编码
     * https://test.zhixue.com/tms/api/common/getNewCourseTag?_=1750064542831&phaseCode=03&subjectCode=02&bizCode=new_course_tag
     */
    public static String XKB_LABEL_XQJ_CODE = "new_course_tag_0001";

    /**
     * 在 新情境 的情况下， 0002代表 数学情境
     * https://test.zhixue.com/tms/api/common/getNewCourseTag?_=1750064542831&phaseCode=03&subjectCode=02&bizCode=new_course_tag
     */
    public static String XKB_LABEL_XQJ_CODE_0002 = "0002";

    /**
     * 新课标属性组装分隔符
     */
    public static String ATTRIBUTE_SEPARATOR = "·";

}
